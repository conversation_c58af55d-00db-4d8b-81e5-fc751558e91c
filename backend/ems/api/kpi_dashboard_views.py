"""
Enhanced KPI Dashboard API Views
Provides real-time KPI data for dashboard visualization
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db.models import Q, Avg, Sum, Count, Max, Min
from datetime import datetime, timedelta
from decimal import Decimal

from ems.models import (
    KPIMetric, KPIMetricValue, Employee, Project, 
    CustomerInvoice, VendorInvoice, Expense, Attendance
)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_dashboard_overview(request):
    """Get comprehensive KPI dashboard overview"""
    try:
        # Get date range from query params
        days = int(request.GET.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get latest KPI values
        latest_kpis = get_latest_kpi_values()
        
        # Get KPI trends
        kpi_trends = get_kpi_trends(start_date, end_date)
        
        # Get real-time metrics
        realtime_metrics = get_realtime_metrics()
        
        # Get performance summary
        performance_summary = get_performance_summary(start_date, end_date)
        
        # Get alerts and notifications
        alerts = get_kpi_alerts()
        
        dashboard_data = {
            'overview': {
                'period': f'{start_date} to {end_date}',
                'total_kpis': KPIMetric.objects.filter(is_active=True).count(),
                'calculated_values': KPIMetricValue.objects.filter(
                    calculated_at__date__range=[start_date, end_date]
                ).count(),
                'last_updated': timezone.now().isoformat()
            },
            'latest_kpis': latest_kpis,
            'trends': kpi_trends,
            'realtime': realtime_metrics,
            'performance': performance_summary,
            'alerts': alerts
        }
        
        return Response(dashboard_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {'error': f'Failed to load dashboard: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_category_dashboard(request, category):
    """Get KPI dashboard for specific category"""
    try:
        # Filter KPIs by category (based on name patterns)
        category_filters = {
            'financial': ['revenue', 'cash', 'profit', 'cost', 'invoice'],
            'hr': ['employee', 'turnover', 'attendance', 'satisfaction', 'training'],
            'operational': ['project', 'utilization', 'quality', 'delivery'],
            'customer': ['customer', 'churn', 'acquisition', 'lifetime']
        }
        
        if category not in category_filters:
            return Response(
                {'error': 'Invalid category'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get KPIs for category
        keywords = category_filters[category]
        kpi_filter = Q()
        for keyword in keywords:
            kpi_filter |= Q(name__icontains=keyword)
        
        category_kpis = KPIMetric.objects.filter(kpi_filter, is_active=True)
        
        # Get latest values for category KPIs
        category_data = []
        for kpi in category_kpis:
            latest_value = KPIMetricValue.objects.filter(
                kpi_metric=kpi
            ).order_by('-calculated_at').first()
            
            if latest_value:
                # Get trend (last 3 values)
                trend_values = list(KPIMetricValue.objects.filter(
                    kpi_metric=kpi
                ).order_by('-calculated_at')[:3].values_list('value', flat=True))
                
                trend_direction = 'stable'
                if len(trend_values) >= 2:
                    if trend_values[0] > trend_values[1]:
                        trend_direction = 'up' if kpi.is_higher_better else 'down'
                    elif trend_values[0] < trend_values[1]:
                        trend_direction = 'down' if kpi.is_higher_better else 'up'
                
                # Determine status based on thresholds
                kpi_status = 'good'
                if kpi.critical_threshold and latest_value.value <= kpi.critical_threshold:
                    kpi_status = 'critical'
                elif kpi.warning_threshold and latest_value.value <= kpi.warning_threshold:
                    kpi_status = 'warning'
                
                category_data.append({
                    'id': kpi.id,
                    'name': kpi.name,
                    'name_ar': kpi.name_ar,
                    'value': float(latest_value.value),
                    'unit': kpi.unit,
                    'target': float(kpi.target_value) if kpi.target_value else None,
                    'status': kpi_status,
                    'trend': trend_direction,
                    'last_updated': latest_value.calculated_at.isoformat(),
                    'period': f'{latest_value.period_start} to {latest_value.period_end}'
                })
        
        return Response({
            'category': category,
            'kpis': category_data,
            'summary': {
                'total_kpis': len(category_data),
                'good_kpis': len([k for k in category_data if k['status'] == 'good']),
                'warning_kpis': len([k for k in category_data if k['status'] == 'warning']),
                'critical_kpis': len([k for k in category_data if k['status'] == 'critical'])
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {'error': f'Failed to load category dashboard: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_detail_chart(request, kpi_id):
    """Get detailed chart data for specific KPI"""
    try:
        kpi = KPIMetric.objects.get(id=kpi_id, is_active=True)
        
        # Get date range
        days = int(request.GET.get('days', 90))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get KPI values for the period
        kpi_values = KPIMetricValue.objects.filter(
            kpi_metric=kpi,
            period_start__gte=start_date
        ).order_by('period_start')
        
        chart_data = {
            'kpi': {
                'id': kpi.id,
                'name': kpi.name,
                'name_ar': kpi.name_ar,
                'description': kpi.description,
                'unit': kpi.unit,
                'target': float(kpi.target_value) if kpi.target_value else None,
                'warning_threshold': float(kpi.warning_threshold) if kpi.warning_threshold else None,
                'critical_threshold': float(kpi.critical_threshold) if kpi.critical_threshold else None
            },
            'data': [
                {
                    'date': value.period_start.isoformat(),
                    'value': float(value.value),
                    'period_end': value.period_end.isoformat(),
                    'data_source': value.data_source,
                    'notes': value.notes
                }
                for value in kpi_values
            ],
            'statistics': {
                'count': kpi_values.count(),
                'average': float(kpi_values.aggregate(avg=Avg('value'))['avg'] or 0),
                'min': float(kpi_values.aggregate(min=Min('value'))['min'] or 0),
                'max': float(kpi_values.aggregate(max=Max('value'))['max'] or 0),
                'latest': float(kpi_values.last().value) if kpi_values.exists() else 0
            }
        }
        
        return Response(chart_data, status=status.HTTP_200_OK)
        
    except KPIMetric.DoesNotExist:
        return Response(
            {'error': 'KPI not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': f'Failed to load KPI chart: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def get_latest_kpi_values():
    """Get latest values for all active KPIs"""
    latest_kpis = []
    
    for kpi in KPIMetric.objects.filter(is_active=True):
        latest_value = KPIMetricValue.objects.filter(
            kpi_metric=kpi
        ).order_by('-calculated_at').first()
        
        if latest_value:
            latest_kpis.append({
                'id': kpi.id,
                'name': kpi.name,
                'value': float(latest_value.value),
                'unit': kpi.unit,
                'target': float(kpi.target_value) if kpi.target_value else None,
                'last_updated': latest_value.calculated_at.isoformat()
            })
    
    return latest_kpis


def get_kpi_trends(start_date, end_date):
    """Get KPI trends for the period"""
    trends = {}
    
    for kpi in KPIMetric.objects.filter(is_active=True)[:10]:  # Top 10 KPIs
        values = KPIMetricValue.objects.filter(
            kpi_metric=kpi,
            period_start__gte=start_date
        ).order_by('period_start')
        
        if values.count() >= 2:
            first_value = values.first().value
            last_value = values.last().value
            
            if first_value != 0:
                change_percent = ((last_value - first_value) / first_value) * 100
                trends[kpi.name] = {
                    'change_percent': float(change_percent),
                    'direction': 'up' if change_percent > 0 else 'down',
                    'values_count': values.count()
                }
    
    return trends


def get_realtime_metrics():
    """Get real-time business metrics"""
    today = timezone.now().date()
    
    return {
        'active_employees': Employee.objects.filter(is_active=True).count(),
        'active_projects': Project.objects.filter(
            status__in=['IN_PROGRESS', 'PLANNING']
        ).count(),
        'monthly_revenue': float(
            CustomerInvoice.objects.filter(
                invoice_date__month=today.month,
                invoice_date__year=today.year,
                status='PAID'
            ).aggregate(total=Sum('paid_amount'))['total'] or 0
        ),
        'pending_invoices': CustomerInvoice.objects.filter(
            status__in=['SENT', 'PARTIAL']
        ).count()
    }


def get_performance_summary(start_date, end_date):
    """Get performance summary for the period"""
    # Calculate key performance indicators
    total_revenue = CustomerInvoice.objects.filter(
        invoice_date__range=[start_date, end_date],
        status='PAID'
    ).aggregate(total=Sum('paid_amount'))['total'] or 0
    
    total_expenses = Expense.objects.filter(
        expense_date__range=[start_date, end_date],
        status='APPROVED'
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    completed_projects = Project.objects.filter(
        actual_end_date__range=[start_date, end_date],
        status='COMPLETED'
    ).count()
    
    return {
        'revenue': float(total_revenue),
        'expenses': float(total_expenses),
        'profit': float(total_revenue - total_expenses),
        'completed_projects': completed_projects,
        'period': f'{start_date} to {end_date}'
    }


def get_kpi_alerts():
    """Get KPI alerts based on thresholds"""
    alerts = []
    
    for kpi in KPIMetric.objects.filter(is_active=True):
        latest_value = KPIMetricValue.objects.filter(
            kpi_metric=kpi
        ).order_by('-calculated_at').first()
        
        if latest_value and kpi.critical_threshold:
            if latest_value.value <= kpi.critical_threshold:
                alerts.append({
                    'type': 'critical',
                    'kpi_name': kpi.name,
                    'current_value': float(latest_value.value),
                    'threshold': float(kpi.critical_threshold),
                    'message': f'{kpi.name} is below critical threshold'
                })
        
        if latest_value and kpi.warning_threshold:
            if latest_value.value <= kpi.warning_threshold:
                alerts.append({
                    'type': 'warning',
                    'kpi_name': kpi.name,
                    'current_value': float(latest_value.value),
                    'threshold': float(kpi.warning_threshold),
                    'message': f'{kpi.name} is below warning threshold'
                })
    
    return alerts
