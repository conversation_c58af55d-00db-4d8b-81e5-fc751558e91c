import React, { useState, useEffect } from 'react'
import {
  Users,
  TrendingUp,
  Calendar,
  Heart,
  Shield,
  UserCheck,
  Activity,
  AlertTriangle,
  RefreshCw,
  Download,
  BarChart3
} from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'
import { apiClient } from '../../services/api'
import logger from '../../utils/logger'

// HR-specific KPI interface
interface HRKPIData {
  id: string
  name: string
  name_ar: string
  current_value?: number
  target_value?: number
  unit?: string
  status?: 'excellent' | 'good' | 'warning' | 'critical'
  trend?: 'up' | 'down' | 'stable'
  achievement_percentage?: number
  last_updated?: string
  category?: string
  hr_specific_data?: {
    department_breakdown?: any[]
    employee_segments?: any[]
    time_series?: any[]
  }
}

interface HRKPIDashboardProps {
  language?: 'ar' | 'en'
  kpiType?: 'performance' | 'retention' | 'attendance' | 'satisfaction' | 'compliance'
  className?: string
}

const HRKPIDashboard: React.FC<HRKPIDashboardProps> = ({
  language = 'en',
  kpiType,
  className = ''
}) => {
  const { user } = useAuth()
  const [kpiData, setKpiData] = useState<HRKPIData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // HR-specific categories
  const hrCategories = [
    { id: 'all', name: 'All HR Metrics', nameAr: 'جميع مقاييس الموارد البشرية' },
    { id: 'performance', name: 'Employee Performance', nameAr: 'أداء الموظفين' },
    { id: 'retention', name: 'Employee Retention', nameAr: 'الاحتفاظ بالموظفين' },
    { id: 'attendance', name: 'Attendance & Time', nameAr: 'الحضور والوقت' },
    { id: 'satisfaction', name: 'Employee Satisfaction', nameAr: 'رضا الموظفين' },
    { id: 'compliance', name: 'HR Compliance', nameAr: 'امتثال الموارد البشرية' }
  ]

  // Load HR-specific KPI data
  const loadHRKPIData = async () => {
    try {
      setLoading(true)
      setError(null)

      logger.info('hrKPI', 'Loading HR-specific KPI data', { kpiType, selectedCategory })

      // Debug: Log the API base URL
      console.log('🔍 API Base URL:', import.meta.env.VITE_API_BASE_URL)
      console.log('🔍 Making API call to: /kpi-metrics/')

      // Get all KPI data first (API doesn't support our custom filters)
      const response = await apiClient.get('/kpi-metrics/')
      const apiData = response.data as any
      const allKPIs = apiData.results || apiData || []

      logger.info('hrKPI', 'Raw KPI data received', { count: allKPIs.length })

      // Client-side filtering based on actual KPI structure
      let filteredKPIs = allKPIs.filter((kpi: any) => {
        // Map actual metric_type to HR categories
        const metricType = kpi.metric_type?.toUpperCase()

        // Define which metric types are relevant for HR
        const hrRelevantTypes = ['EMPLOYEE', 'OPERATIONAL', 'FINANCIAL']

        // First filter: only HR-relevant KPIs
        if (!hrRelevantTypes.includes(metricType)) {
          return false
        }

        // Second filter: by specific kpiType if provided
        if (kpiType) {
          switch (kpiType) {
            case 'performance':
              // Performance KPIs: Employee satisfaction, operational metrics
              return metricType === 'EMPLOYEE' || metricType === 'OPERATIONAL'
            case 'retention':
              // Retention KPIs: Employee-related metrics
              return metricType === 'EMPLOYEE'
            case 'attendance':
              // Attendance KPIs: Operational metrics related to time/attendance
              return metricType === 'OPERATIONAL' &&
                     (kpi.name?.toLowerCase().includes('time') ||
                      kpi.name?.toLowerCase().includes('attendance') ||
                      kpi.name?.toLowerCase().includes('processing'))
            case 'satisfaction':
              // Satisfaction KPIs: Employee satisfaction metrics
              return metricType === 'EMPLOYEE' &&
                     (kpi.name?.toLowerCase().includes('satisfaction') ||
                      kpi.name?.toLowerCase().includes('score'))
            case 'compliance':
              // Compliance KPIs: Operational and some financial metrics
              return metricType === 'OPERATIONAL' ||
                     (metricType === 'FINANCIAL' && kpi.name?.toLowerCase().includes('cost'))
            default:
              return true
          }
        }

        // Third filter: by selected category
        if (selectedCategory !== 'all') {
          switch (selectedCategory) {
            case 'performance':
              return metricType === 'EMPLOYEE' || metricType === 'OPERATIONAL'
            case 'retention':
              return metricType === 'EMPLOYEE'
            case 'attendance':
              return metricType === 'OPERATIONAL'
            case 'satisfaction':
              return metricType === 'EMPLOYEE'
            case 'compliance':
              return metricType === 'OPERATIONAL' || metricType === 'FINANCIAL'
            default:
              return true
          }
        }

        return true
      })

      logger.info('hrKPI', 'Filtered KPI data', {
        originalCount: allKPIs.length,
        filteredCount: filteredKPIs.length,
        kpiType,
        selectedCategory
      })

      // Transform data for HR-specific display
      const transformedKPIs = filteredKPIs.map((kpi: any) => ({
        id: kpi.id?.toString() || Math.random().toString(),
        name: language === 'ar' ? (kpi.name_ar || kpi.name) : kpi.name,
        name_ar: kpi.name_ar || kpi.name,
        current_value: kpi.current_value?.value ?? kpi.value ?? 0,
        target_value: kpi.target_value ?? null,
        unit: kpi.unit || '',
        status: getHRKPIStatus(kpi),
        trend: getHRKPITrend(kpi),
        achievement_percentage: kpi.achievement_percentage ?? calculateAchievement(kpi),
        last_updated: kpi.last_updated || kpi.updated_at,
        category: mapMetricTypeToHRCategory(kpi.metric_type),
        hr_specific_data: {
          department_breakdown: kpi.department_breakdown || [],
          employee_segments: kpi.employee_segments || [],
          time_series: kpi.time_series || []
        }
      }))

      setKpiData(transformedKPIs)
      setLastUpdated(new Date())

      logger.info('hrKPI', 'HR KPI data loaded successfully', {
        count: transformedKPIs.length,
        kpiType,
        category: selectedCategory
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load HR KPI data'
      setError(errorMessage)
      console.error('❌ HR KPI API Error:', err)
      console.error('❌ Error details:', {
        message: errorMessage,
        status: (err as any)?.status,
        response: (err as any)?.response?.data
      })
      logger.error('hrKPI', 'Error loading HR KPI data:', err)
    } finally {
      setLoading(false)
    }
  }

  // Helper function to map metric_type to HR categories
  const mapMetricTypeToHRCategory = (metricType: string): string => {
    switch (metricType?.toUpperCase()) {
      case 'EMPLOYEE': return 'Employee'
      case 'OPERATIONAL': return 'Operations'
      case 'FINANCIAL': return 'Finance'
      case 'ASSET': return 'Assets'
      default: return 'General'
    }
  }

  // Helper functions for HR-specific logic
  const getHRKPIStatus = (kpi: any): 'excellent' | 'good' | 'warning' | 'critical' => {
    const value = kpi.current_value?.value ?? kpi.value ?? 0
    const target = kpi.target_value ?? 100
    const percentage = target > 0 ? (value / target) * 100 : 0

    if (percentage >= 95) return 'excellent'
    if (percentage >= 80) return 'good'
    if (percentage >= 60) return 'warning'
    return 'critical'
  }

  const getHRKPITrend = (kpi: any): 'up' | 'down' | 'stable' => {
    if (kpi.trend) {
      if (typeof kpi.trend === 'object' && kpi.trend.direction) {
        return kpi.trend.direction
      }
      if (typeof kpi.trend === 'string') {
        return ['up', 'down', 'stable'].includes(kpi.trend) ? kpi.trend as any : 'stable'
      }
    }
    return 'stable'
  }

  const calculateAchievement = (kpi: any): number => {
    const value = kpi.current_value?.value ?? kpi.value ?? 0
    const target = kpi.target_value ?? 100
    return target > 0 ? Math.min((value / target) * 100, 100) : 0
  }

  // Sync selectedCategory with kpiType prop
  useEffect(() => {
    if (kpiType && kpiType !== selectedCategory) {
      setSelectedCategory(kpiType)
    }
  }, [kpiType])

  // Load data when component mounts or filters change
  useEffect(() => {
    loadHRKPIData()
  }, [kpiType, selectedCategory])

  // Get icon for KPI category
  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'performance': return TrendingUp
      case 'retention': return UserCheck
      case 'attendance': return Calendar
      case 'satisfaction': return Heart
      case 'compliance': return Shield
      default: return Users
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-400 bg-green-500/20'
      case 'good': return 'text-blue-400 bg-blue-500/20'
      case 'warning': return 'text-yellow-400 bg-yellow-500/20'
      case 'critical': return 'text-red-400 bg-red-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    }
  }

  // Get trend icon
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down': return <TrendingUp className="h-4 w-4 text-red-400 rotate-180" />
      default: return <Activity className="h-4 w-4 text-gray-400" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 text-white/60">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>{language === 'ar' ? 'جاري تحميل بيانات الموارد البشرية...' : 'Loading HR data...'}</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-300 mb-2">
          <AlertTriangle className="h-5 w-5" />
          <span className="font-semibold">
            {language === 'ar' ? 'خطأ في تحميل بيانات الموارد البشرية' : 'HR Data Loading Error'}
          </span>
        </div>
        <p className="text-red-200 text-sm mb-4">{error}</p>
        <button
          onClick={loadHRKPIData}
          className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
        </button>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* HR Dashboard Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 rounded-lg bg-gradient-to-r from-green-500 to-teal-500">
            <Users className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'لوحة تحكم الموارد البشرية' : 'HR Analytics Dashboard'}
            </h1>
            <p className="text-white/60">
              {language === 'ar' ? 'مقاييس الأداء الرئيسية للموارد البشرية' : 'Human Resources Key Performance Indicators'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={loadHRKPIData}
            className="px-3 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
          <button className="px-3 py-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors">
            <Download className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <h3 className="text-lg font-semibold text-white mb-3">
          {language === 'ar' ? 'فئات الموارد البشرية' : 'HR Categories'}
        </h3>
        <div className="flex flex-wrap gap-2">
          {hrCategories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedCategory === category.id
                  ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                  : 'bg-white/10 text-white/60 hover:bg-white/20'
              }`}
            >
              {language === 'ar' ? category.nameAr : category.name}
            </button>
          ))}
        </div>
      </div>

      {/* HR KPI Grid */}
      {kpiData.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-white/40 mb-4">
            <BarChart3 className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-white/60">
            {language === 'ar' ? 'لا توجد بيانات موارد بشرية متاحة' : 'No HR data available'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {kpiData.map((kpi) => {
            const CategoryIcon = getCategoryIcon(kpi.category || '')
            return (
              <div
                key={kpi.id}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-white/20 transition-all"
              >
                {/* KPI Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <CategoryIcon className="h-5 w-5 text-green-400" />
                    <span className="text-xs text-white/60 uppercase tracking-wide">
                      {kpi.category}
                    </span>
                  </div>
                  {getTrendIcon(kpi.trend || 'stable')}
                </div>

                {/* KPI Name */}
                <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                  {kpi.name}
                </h3>

                {/* KPI Value */}
                <div className="flex items-baseline space-x-2 mb-3">
                  <span className="text-2xl font-bold text-white">
                    {kpi.current_value?.toLocaleString() || '0'}
                  </span>
                  {kpi.unit && (
                    <span className="text-sm text-white/60">{kpi.unit}</span>
                  )}
                </div>

                {/* Progress Bar */}
                {kpi.target_value && (
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-white/60 mb-1">
                      <span>Progress</span>
                      <span>{kpi.achievement_percentage?.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-teal-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${Math.min(kpi.achievement_percentage || 0, 100)}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Status Badge */}
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(kpi.status || 'good')}`}>
                    {kpi.status?.charAt(0).toUpperCase() + kpi.status?.slice(1) || 'Good'}
                  </span>
                  {kpi.last_updated && (
                    <span className="text-xs text-white/40">
                      {new Date(kpi.last_updated).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <div className="text-center text-xs text-white/40">
          {language === 'ar' ? 'آخر تحديث: ' : 'Last updated: '}
          {lastUpdated.toLocaleString()}
        </div>
      )}
    </div>
  )
}

export default HRKPIDashboard
