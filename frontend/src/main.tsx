import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { store } from './store'
import './index.css'
import App from './App.tsx'

// SECURITY FIX: Initialize CSRF protection
import { initializeCSRFProtection } from './utils/csrfToken'

// MOBILE FIX: Initialize mobile optimization
import { mobileOptimization } from './utils/mobileOptimization'
import { log } from './utils/logger'

// Performance optimizations - CONSOLIDATED: Use only the control panel
import { performanceControlPanel } from './utils/performanceControlPanel'

// SECURITY IMPROVEMENT: Initialize comprehensive security features
import { initializeSecurity } from './utils/securityHeaders'
// TEMPORARILY DISABLE XSS MONITORING TO FIX INFINITE LOOP
// import { monitorXSSAttempts } from './utils/csrfToken'

// ACCESSIBILITY IMPROVEMENT: Initialize accessibility features
import { accessibility, announcePageChange } from './utils/accessibility'
import { analytics } from './utils/analytics'

// TEMPORARILY DISABLE ALL CSRF PROTECTION TO FIX INFINITE LOOP
// TODO: Re-implement CSRF protection with proper safeguards
// import('./utils/reduxCSRF').then(({ initializeCSRFProtectionRedux }) => {
//   initializeCSRFProtectionRedux()
// })

// Initialize security headers and CSP
initializeSecurity()

// TEMPORARILY DISABLE XSS MONITORING TO FIX INFINITE LOOP
// monitorXSSAttempts()

// Initialize accessibility system
accessibility.updateConfig({
  enableScreenReader: true,
  enableKeyboardNavigation: true,
  announcePageChanges: true,
  announceFormErrors: true,
  announceStatusUpdates: true
})

// Announce application ready
announcePageChange('نظام إدارة الموظفين')

// FIXED: Use production-safe logging
log.debug('mobile', 'Mobile optimization initialized', mobileOptimization.getDeviceInfo())
log.debug('performance', 'Performance optimizations initialized')
log.debug('performance', 'Performance control panel initialized')
log.debug('performance', 'Enable debug mode with: ?debug=performance or window.enablePerformanceDebug()')
log.debug('memory', 'Memory optimization initialized')

// Memory monitoring initialized via performance control panel

// FIXED: Eliminate unused preload warnings by only preloading what's actually needed
const smartResourceLoading = async () => {
  try {
    // FIXED: Only preload resources that will definitely be used
    // Check if Inter font is actually needed and not already loaded
    if (document.fonts && !document.fonts.check('16px Inter')) {
      // Only preload if we're actually using Inter font in the app
      const usesInterFont = document.querySelector('[class*="font-inter"]') ||
                           getComputedStyle(document.body).fontFamily.includes('Inter')

      // Font preloading handled by performance control panel
    }

    // FIXED: Don't preload any CSS - Vite handles this optimally
    // This completely eliminates preload warnings for CSS files

  } catch (error) {
    // Silent fail for resource loading optimization - no console noise
    if (process.env.NODE_ENV === 'development') {
      console.debug('Smart resource loading skipped:', error)
    }
  }
}

// Delay resource loading to avoid blocking initial render
setTimeout(smartResourceLoading, 100)

// PERFORMANCE FIX: Prefetch critical API data instead of preloading as scripts
const prefetchCriticalData = async () => {
  try {
    // Only prefetch if user might be authenticated (has cookies)
    if (document.cookie.includes('access_token')) {
      // Prefetch user data
      fetch('/api/auth/user/', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' }
      }).catch(() => {}) // Silent fail for prefetch

      // Prefetch departments data
      fetch('/api/departments/', {
        credentials: 'include',
        headers: { 'Accept': 'application/json' }
      }).catch(() => {}) // Silent fail for prefetch
    }
  } catch (error) {
    // Silent fail for prefetch operations
  }
}

// Start prefetching after a short delay to not block initial render
setTimeout(prefetchCriticalData, 1000)

// Performance validation and cleanup handled by performance control panel

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </StrictMode>,
)
