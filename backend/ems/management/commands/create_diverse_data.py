"""
Management command to create diverse and varied business data
This demonstrates real data variation and business patterns
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import random

from ems.models import (
    Employee, Department, Customer, Vendor, Project, Task, 
    Expense, CustomerInvoice, VendorInvoice, Attendance
)


class Command(BaseCommand):
    help = 'Create diverse and varied business data to show real patterns'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing operational data before creating new'
        )

    def handle(self, *args, **options):
        clear_existing = options['clear_existing']
        
        self.stdout.write(
            self.style.SUCCESS('🎨 Creating Diverse Business Data Patterns')
        )
        
        if clear_existing:
            self.clear_existing_data()
        
        # Create varied business scenarios
        self.create_seasonal_revenue_patterns()
        self.create_project_performance_variations()
        self.create_employee_performance_patterns()
        self.create_customer_behavior_variations()
        self.create_expense_patterns()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Diverse business data created successfully!')
        )

    def clear_existing_data(self):
        """Clear existing operational data"""
        self.stdout.write('🧹 Clearing existing operational data...')
        
        # Keep employees and basic structure, clear operational data
        CustomerInvoice.objects.all().delete()
        VendorInvoice.objects.all().delete()
        Expense.objects.all().delete()
        Task.objects.all().delete()
        # Keep projects but clear some
        Project.objects.filter(name__startswith='Test').delete()
        
        self.stdout.write('   ✅ Existing data cleared')

    def create_seasonal_revenue_patterns(self):
        """Create realistic seasonal revenue patterns"""
        self.stdout.write('📈 Creating seasonal revenue patterns...')
        
        customers = list(Customer.objects.filter(status='active'))
        finance_employee = Employee.objects.filter(is_active=True).first()
        
        if not customers or not finance_employee:
            self.stdout.write('   ⚠️ No customers or employees found')
            return
        
        # Create 12 months of varied revenue data
        for month_offset in range(12):
            invoice_date = timezone.now().date() - timedelta(days=30 * month_offset)
            month = invoice_date.month
            
            # Seasonal multipliers
            seasonal_multipliers = {
                1: 0.8,   # January - slow start
                2: 0.9,   # February - building up
                3: 1.1,   # March - Q1 push
                4: 1.0,   # April - normal
                5: 1.2,   # May - spring boost
                6: 1.1,   # June - mid-year
                7: 0.9,   # July - summer slow
                8: 0.8,   # August - vacation
                9: 1.3,   # September - back to business
                10: 1.4,  # October - Q4 push
                11: 1.6,  # November - holiday season
                12: 1.8   # December - year-end rush
            }
            
            multiplier = seasonal_multipliers.get(month, 1.0)
            
            # Create 2-8 invoices per month based on season
            invoice_count = int(random.randint(2, 8) * multiplier)
            
            for i in range(invoice_count):
                customer = random.choice(customers)
                
                # Base amount varies by season
                base_amount = random.randint(50000, 300000)
                seasonal_amount = int(base_amount * multiplier)
                
                # Add some randomness
                variation = random.uniform(0.7, 1.3)
                final_amount = int(seasonal_amount * variation)
                
                subtotal = Decimal(str(final_amount))
                tax_amount = (subtotal * Decimal('0.15')).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount
                
                # Payment probability varies by season
                payment_prob = 0.95 if month in [11, 12] else 0.85
                is_paid = random.random() < payment_prob
                
                CustomerInvoice.objects.create(
                    customer=customer,
                    invoice_number=f"SEAS-{invoice_date.strftime('%Y%m')}-{i+1:04d}",
                    invoice_date=invoice_date,
                    due_date=invoice_date + timedelta(days=30),
                    description=f"Seasonal services - {invoice_date.strftime('%B %Y')}",
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    paid_amount=total_amount if is_paid else Decimal('0'),
                    status='PAID' if is_paid else random.choice(['SENT', 'PARTIAL']),
                    created_by=finance_employee
                )
        
        self.stdout.write('   ✅ Seasonal revenue patterns created')

    def create_project_performance_variations(self):
        """Create projects with varied performance patterns"""
        self.stdout.write('📊 Creating project performance variations...')
        
        employees = list(Employee.objects.filter(is_active=True))
        managers = [emp for emp in employees if 'Manager' in emp.position or 'Officer' in emp.position]
        
        if not managers:
            managers = employees[:3]
        
        # Different project types with different success patterns
        project_types = [
            {
                'name': 'High-Performance AI Analytics',
                'budget': 2500000,
                'success_rate': 0.9,
                'duration': 180,
                'complexity': 'HIGH'
            },
            {
                'name': 'Standard Web Development',
                'budget': 800000,
                'success_rate': 0.95,
                'duration': 120,
                'complexity': 'MEDIUM'
            },
            {
                'name': 'Challenging Legacy Migration',
                'budget': 1800000,
                'success_rate': 0.6,
                'duration': 300,
                'complexity': 'HIGH'
            },
            {
                'name': 'Simple Mobile App',
                'budget': 400000,
                'success_rate': 0.98,
                'duration': 90,
                'complexity': 'LOW'
            },
            {
                'name': 'Complex Enterprise Integration',
                'budget': 3200000,
                'success_rate': 0.7,
                'duration': 365,
                'complexity': 'HIGH'
            }
        ]
        
        for i, proj_type in enumerate(project_types):
            # Create multiple instances of each project type
            for instance in range(2):
                start_date = timezone.now().date() - timedelta(days=random.randint(30, 300))
                planned_duration = proj_type['duration']
                
                # Success affects actual duration
                if random.random() < proj_type['success_rate']:
                    # Successful project
                    actual_duration = int(planned_duration * random.uniform(0.9, 1.1))
                    status = 'COMPLETED'
                    budget_variance = random.uniform(0.95, 1.05)
                else:
                    # Problematic project
                    actual_duration = int(planned_duration * random.uniform(1.2, 1.8))
                    status = random.choice(['IN_PROGRESS', 'ON_HOLD', 'CANCELLED'])
                    budget_variance = random.uniform(1.1, 1.4)
                
                end_date = start_date + timedelta(days=actual_duration)
                actual_budget = int(proj_type['budget'] * budget_variance)
                
                project = Project.objects.create(
                    name=f"{proj_type['name']} - Phase {instance + 1}",
                    name_ar=f"مشروع {proj_type['complexity']} - المرحلة {instance + 1}",
                    description=f"{proj_type['complexity']} complexity project with {proj_type['success_rate']*100:.0f}% success rate",
                    client=f"Client-{proj_type['complexity']}-{instance + 1}",
                    project_manager=random.choice(managers),
                    start_date=start_date,
                    end_date=end_date,
                    budget_amount=Decimal(str(actual_budget)),
                    status=status,
                    priority=proj_type['complexity']
                )
                
                # Create tasks for each project with varied completion rates
                self.create_project_tasks(project, proj_type)
        
        self.stdout.write('   ✅ Project performance variations created')

    def create_project_tasks(self, project, proj_type):
        """Create tasks for a project with realistic completion patterns"""
        employees = list(Employee.objects.filter(is_active=True))
        task_count = random.randint(5, 15)
        
        task_templates = [
            'Requirements Analysis', 'System Design', 'Database Design',
            'Frontend Development', 'Backend Development', 'API Integration',
            'Testing & QA', 'User Training', 'Documentation', 'Deployment'
        ]
        
        for i in range(task_count):
            task_name = f"{random.choice(task_templates)} - {project.name[:20]}"
            
            # Task dates within project timeline
            task_start = project.start_date + timedelta(days=random.randint(0, 30))
            task_duration = random.randint(3, 21)
            task_end = task_start + timedelta(days=task_duration)
            
            # Task completion based on project success rate
            if random.random() < proj_type['success_rate']:
                status = 'COMPLETED'
                completion_date = task_end
            else:
                status = random.choice(['IN_PROGRESS', 'ON_HOLD', 'NOT_STARTED'])
                completion_date = None
            
            Task.objects.create(
                project=project,
                title=task_name,
                title_ar=f"مهمة {i+1} - {project.name_ar[:20]}",
                description=f"Task for {proj_type['complexity']} complexity project",
                assigned_to=random.choice(employees),
                created_by=project.project_manager,
                start_date=task_start,
                due_date=task_end,
                completion_date=completion_date,
                status=status,
                priority=project.priority,
                estimated_hours=Decimal(str(random.randint(8, 40)))
            )

    def create_employee_performance_patterns(self):
        """Create varied employee performance patterns"""
        self.stdout.write('👥 Creating employee performance patterns...')
        
        employees = list(Employee.objects.filter(is_active=True))
        
        # Create attendance patterns for the last 90 days
        for days_ago in range(90):
            date = timezone.now().date() - timedelta(days=days_ago)
            
            # Skip weekends
            if date.weekday() in [4, 5]:  # Friday, Saturday
                continue
            
            for employee in employees:
                # Different attendance patterns by employee type
                if 'Manager' in employee.position or 'Officer' in employee.position:
                    attendance_rate = 0.98  # Managers rarely absent
                elif 'Senior' in employee.position:
                    attendance_rate = 0.95  # Senior staff very reliable
                else:
                    attendance_rate = 0.92  # Regular staff good attendance
                
                # Seasonal variations (summer lower attendance)
                if date.month in [7, 8]:  # July, August
                    attendance_rate *= 0.9
                
                if random.random() < attendance_rate:
                    # Present - varied arrival times
                    if 'Manager' in employee.position:
                        check_in_hour = random.randint(7, 8)  # Early arrivals
                    else:
                        check_in_hour = random.randint(8, 9)  # Normal arrivals
                    
                    check_in_minute = random.randint(0, 59)
                    check_out_hour = random.randint(16, 18)
                    check_out_minute = random.randint(0, 59)
                    
                    check_in_time = timezone.make_aware(
                        datetime.combine(date, datetime.min.time().replace(
                            hour=check_in_hour, minute=check_in_minute
                        ))
                    )
                    
                    check_out_time = timezone.make_aware(
                        datetime.combine(date, datetime.min.time().replace(
                            hour=check_out_hour, minute=check_out_minute
                        ))
                    )
                    
                    hours_worked = (check_out_time - check_in_time).total_seconds() / 3600
                    
                    Attendance.objects.create(
                        employee=employee,
                        date=date,
                        check_in=check_in_time,
                        check_out=check_out_time,
                        hours_worked=Decimal(str(round(hours_worked, 2))),
                        is_present=True,
                        notes=f"Regular attendance - {date}"
                    )
        
        self.stdout.write('   ✅ Employee performance patterns created')

    def create_customer_behavior_variations(self):
        """Create varied customer behavior patterns"""
        self.stdout.write('🤝 Creating customer behavior variations...')
        
        # This would create different customer segments with varied behaviors
        # For now, we've already created varied invoice patterns above
        self.stdout.write('   ✅ Customer behavior variations included in revenue patterns')

    def create_expense_patterns(self):
        """Create realistic expense patterns"""
        self.stdout.write('💳 Creating expense patterns...')
        
        employees = list(Employee.objects.filter(is_active=True))
        
        # Different expense patterns by role
        for employee in employees:
            # Managers have more expenses
            if 'Manager' in employee.position or 'Officer' in employee.position:
                monthly_expenses = random.randint(3, 8)
                expense_range = (500, 5000)
            elif 'Senior' in employee.position:
                monthly_expenses = random.randint(1, 4)
                expense_range = (200, 2000)
            else:
                monthly_expenses = random.randint(0, 2)
                expense_range = (100, 800)
            
            # Create expenses for last 6 months
            for month_offset in range(6):
                for _ in range(monthly_expenses):
                    expense_date = timezone.now().date() - timedelta(
                        days=30 * month_offset + random.randint(0, 28)
                    )
                    
                    categories = ['TRAVEL', 'OFFICE_SUPPLIES', 'TRAINING', 'MEALS', 'TRANSPORTATION']
                    category = random.choice(categories)
                    amount = Decimal(str(random.randint(*expense_range)))
                    
                    # Approval rate varies by amount
                    if amount < 1000:
                        status = 'APPROVED'
                    elif amount < 3000:
                        status = random.choice(['APPROVED', 'APPROVED', 'PENDING'])
                    else:
                        status = random.choice(['APPROVED', 'PENDING', 'REJECTED'])
                    
                    Expense.objects.create(
                        employee=employee,
                        title=f"{category.replace('_', ' ').title()} - {expense_date.strftime('%B')}",
                        title_ar=f"مصروف {category} - {expense_date.strftime('%B')}",
                        description=f"Business expense for {category.lower().replace('_', ' ')}",
                        category=category,
                        amount=amount,
                        currency='SAR',
                        expense_date=expense_date,
                        status=status
                    )
        
        self.stdout.write('   ✅ Expense patterns created')
