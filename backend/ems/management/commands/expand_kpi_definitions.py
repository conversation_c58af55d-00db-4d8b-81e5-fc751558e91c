"""
Management command to expand KPI definitions based on business needs
This adds comprehensive KPI metrics for advanced business intelligence
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal

from ems.models import KPIMetric, Employee


class Command(BaseCommand):
    help = 'Expand KPI definitions based on business needs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--category',
            type=str,
            choices=['all', 'financial', 'operational', 'hr', 'customer', 'strategic'],
            default='all',
            help='KPI category to expand'
        )

    def handle(self, *args, **options):
        category = options['category']
        
        self.stdout.write(
            self.style.SUCCESS(f'📊 Expanding KPI definitions - {category.upper()} category')
        )
        
        creator = Employee.objects.first()
        if not creator:
            self.stdout.write(
                self.style.ERROR('❌ No employees found. Cannot create KPIs.')
            )
            return
        
        created_count = 0
        
        if category in ['all', 'financial']:
            created_count += self.create_financial_kpis(creator)
        
        if category in ['all', 'operational']:
            created_count += self.create_operational_kpis(creator)
        
        if category in ['all', 'hr']:
            created_count += self.create_hr_kpis(creator)
        
        if category in ['all', 'customer']:
            created_count += self.create_customer_kpis(creator)
        
        if category in ['all', 'strategic']:
            created_count += self.create_strategic_kpis(creator)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Created {created_count} new KPI definitions')
        )

    def create_financial_kpis(self, creator):
        """Create comprehensive financial KPIs"""
        financial_kpis = [
            {
                'name': 'Gross Profit Margin',
                'name_ar': 'هامش الربح الإجمالي',
                'description': 'Percentage of revenue remaining after direct costs',
                'description_ar': 'نسبة الإيرادات المتبقية بعد التكاليف المباشرة',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': '(Revenue - Direct_Costs) / Revenue * 100',
                'target_value': Decimal('40.0'),
                'warning_threshold': Decimal('35.0'),
                'critical_threshold': Decimal('30.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Operating Cash Flow Ratio',
                'name_ar': 'نسبة التدفق النقدي التشغيلي',
                'description': 'Operating cash flow divided by current liabilities',
                'description_ar': 'التدفق النقدي التشغيلي مقسوم على الخصوم المتداولة',
                'metric_type': 'RATIO',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Operating_Cash_Flow / Current_Liabilities',
                'target_value': Decimal('1.2'),
                'warning_threshold': Decimal('1.0'),
                'critical_threshold': Decimal('0.8'),
                'unit': 'ratio',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Accounts Receivable Turnover',
                'name_ar': 'معدل دوران الذمم المدينة',
                'description': 'How quickly company collects outstanding invoices',
                'description_ar': 'سرعة تحصيل الفواتير المستحقة',
                'metric_type': 'RATIO',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Net_Credit_Sales / Average_Accounts_Receivable',
                'target_value': Decimal('12.0'),
                'warning_threshold': Decimal('8.0'),
                'critical_threshold': Decimal('6.0'),
                'unit': 'times/year',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Cost Per Acquisition',
                'name_ar': 'تكلفة اكتساب العميل',
                'description': 'Average cost to acquire a new customer',
                'description_ar': 'متوسط تكلفة اكتساب عميل جديد',
                'metric_type': 'CURRENCY',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Total_Marketing_Sales_Costs / New_Customers_Acquired',
                'target_value': Decimal('5000.0'),
                'warning_threshold': Decimal('7500.0'),
                'critical_threshold': Decimal('10000.0'),
                'unit': 'SAR',
                'is_higher_better': False,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Return on Investment (ROI)',
                'name_ar': 'العائد على الاستثمار',
                'description': 'Percentage return on business investments',
                'description_ar': 'نسبة العائد على استثمارات الأعمال',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': '(Investment_Gain - Investment_Cost) / Investment_Cost * 100',
                'target_value': Decimal('15.0'),
                'warning_threshold': Decimal('10.0'),
                'critical_threshold': Decimal('5.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            }
        ]
        
        return self._create_kpis(financial_kpis, creator)

    def create_operational_kpis(self, creator):
        """Create comprehensive operational KPIs"""
        operational_kpis = [
            {
                'name': 'Project Delivery Success Rate',
                'name_ar': 'معدل نجاح تسليم المشاريع',
                'description': 'Percentage of projects delivered on time and budget',
                'description_ar': 'نسبة المشاريع المسلمة في الوقت والميزانية المحددة',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Successful_Projects / Total_Projects * 100',
                'target_value': Decimal('85.0'),
                'warning_threshold': Decimal('75.0'),
                'critical_threshold': Decimal('65.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Resource Utilization Rate',
                'name_ar': 'معدل استخدام الموارد',
                'description': 'Percentage of available resources actively utilized',
                'description_ar': 'نسبة الموارد المتاحة المستخدمة بفعالية',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Utilized_Resources / Available_Resources * 100',
                'target_value': Decimal('80.0'),
                'warning_threshold': Decimal('70.0'),
                'critical_threshold': Decimal('60.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'WEEKLY'
            },
            {
                'name': 'Quality Score',
                'name_ar': 'نقاط الجودة',
                'description': 'Overall quality rating based on deliverables',
                'description_ar': 'تقييم الجودة الإجمالي بناءً على المخرجات',
                'metric_type': 'SCORE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Sum(Quality_Ratings) / Count(Deliverables)',
                'target_value': Decimal('4.5'),
                'warning_threshold': Decimal('4.0'),
                'critical_threshold': Decimal('3.5'),
                'unit': '/5.0',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'System Uptime',
                'name_ar': 'وقت تشغيل النظام',
                'description': 'Percentage of time systems are operational',
                'description_ar': 'نسبة الوقت الذي تكون فيه الأنظمة تعمل',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Uptime_Hours / Total_Hours * 100',
                'target_value': Decimal('99.5'),
                'warning_threshold': Decimal('99.0'),
                'critical_threshold': Decimal('98.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'DAILY'
            }
        ]
        
        return self._create_kpis(operational_kpis, creator)

    def create_hr_kpis(self, creator):
        """Create comprehensive HR KPIs"""
        hr_kpis = [
            {
                'name': 'Employee Net Promoter Score',
                'name_ar': 'نقاط ترشيح الموظفين',
                'description': 'Likelihood of employees recommending company as workplace',
                'description_ar': 'احتمالية ترشيح الموظفين للشركة كمكان عمل',
                'metric_type': 'SCORE',
                'calculation_method': 'SURVEY',
                'target_value': Decimal('50.0'),
                'warning_threshold': Decimal('30.0'),
                'critical_threshold': Decimal('10.0'),
                'unit': 'NPS',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            },
            {
                'name': 'Time to Fill Positions',
                'name_ar': 'وقت ملء الوظائف',
                'description': 'Average days to fill open positions',
                'description_ar': 'متوسط الأيام لملء الوظائف الشاغرة',
                'metric_type': 'DURATION',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Sum(Days_to_Fill) / Count(Filled_Positions)',
                'target_value': Decimal('30.0'),
                'warning_threshold': Decimal('45.0'),
                'critical_threshold': Decimal('60.0'),
                'unit': 'days',
                'is_higher_better': False,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Training ROI',
                'name_ar': 'عائد الاستثمار في التدريب',
                'description': 'Return on investment for employee training programs',
                'description_ar': 'عائد الاستثمار في برامج تدريب الموظفين',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': '(Training_Benefits - Training_Costs) / Training_Costs * 100',
                'target_value': Decimal('200.0'),
                'warning_threshold': Decimal('150.0'),
                'critical_threshold': Decimal('100.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            },
            {
                'name': 'Absenteeism Rate',
                'name_ar': 'معدل الغياب',
                'description': 'Percentage of scheduled work days missed',
                'description_ar': 'نسبة أيام العمل المجدولة المفقودة',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Absent_Days / Scheduled_Work_Days * 100',
                'target_value': Decimal('3.0'),
                'warning_threshold': Decimal('5.0'),
                'critical_threshold': Decimal('8.0'),
                'unit': '%',
                'is_higher_better': False,
                'frequency': 'MONTHLY'
            }
        ]
        
        return self._create_kpis(hr_kpis, creator)

    def create_customer_kpis(self, creator):
        """Create comprehensive customer KPIs"""
        customer_kpis = [
            {
                'name': 'Customer Lifetime Value',
                'name_ar': 'القيمة الدائمة للعميل',
                'description': 'Total revenue expected from customer relationship',
                'description_ar': 'إجمالي الإيرادات المتوقعة من علاقة العميل',
                'metric_type': 'CURRENCY',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Average_Purchase_Value * Purchase_Frequency * Customer_Lifespan',
                'target_value': Decimal('100000.0'),
                'warning_threshold': Decimal('75000.0'),
                'critical_threshold': Decimal('50000.0'),
                'unit': 'SAR',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            },
            {
                'name': 'Customer Churn Rate',
                'name_ar': 'معدل فقدان العملاء',
                'description': 'Percentage of customers lost in a period',
                'description_ar': 'نسبة العملاء المفقودين في فترة معينة',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': 'Lost_Customers / Total_Customers * 100',
                'target_value': Decimal('5.0'),
                'warning_threshold': Decimal('8.0'),
                'critical_threshold': Decimal('12.0'),
                'unit': '%',
                'is_higher_better': False,
                'frequency': 'MONTHLY'
            },
            {
                'name': 'Customer Satisfaction Score',
                'name_ar': 'نقاط رضا العملاء',
                'description': 'Average customer satisfaction rating',
                'description_ar': 'متوسط تقييم رضا العملاء',
                'metric_type': 'SCORE',
                'calculation_method': 'SURVEY',
                'target_value': Decimal('4.5'),
                'warning_threshold': Decimal('4.0'),
                'critical_threshold': Decimal('3.5'),
                'unit': '/5.0',
                'is_higher_better': True,
                'frequency': 'MONTHLY'
            }
        ]
        
        return self._create_kpis(customer_kpis, creator)

    def create_strategic_kpis(self, creator):
        """Create comprehensive strategic KPIs"""
        strategic_kpis = [
            {
                'name': 'Market Share Growth',
                'name_ar': 'نمو الحصة السوقية',
                'description': 'Percentage growth in market share',
                'description_ar': 'نسبة النمو في الحصة السوقية',
                'metric_type': 'PERCENTAGE',
                'calculation_method': 'FORMULA',
                'custom_formula': '(Current_Market_Share - Previous_Market_Share) / Previous_Market_Share * 100',
                'target_value': Decimal('10.0'),
                'warning_threshold': Decimal('5.0'),
                'critical_threshold': Decimal('0.0'),
                'unit': '%',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            },
            {
                'name': 'Innovation Index',
                'name_ar': 'مؤشر الابتكار',
                'description': 'Measure of innovation activities and outcomes',
                'description_ar': 'مقياس أنشطة ونتائج الابتكار',
                'metric_type': 'SCORE',
                'calculation_method': 'COMPOSITE',
                'target_value': Decimal('75.0'),
                'warning_threshold': Decimal('60.0'),
                'critical_threshold': Decimal('45.0'),
                'unit': '/100',
                'is_higher_better': True,
                'frequency': 'QUARTERLY'
            }
        ]
        
        return self._create_kpis(strategic_kpis, creator)

    def _create_kpis(self, kpi_definitions, creator):
        """Helper method to create KPIs from definitions"""
        created_count = 0
        
        for kpi_data in kpi_definitions:
            kpi, created = KPIMetric.objects.get_or_create(
                name=kpi_data['name'],
                defaults={
                    **kpi_data,
                    'created_by': creator,
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'  ✅ Created KPI: {kpi.name}')
            else:
                self.stdout.write(f'  ⚠️ KPI already exists: {kpi.name}')
        
        return created_count
