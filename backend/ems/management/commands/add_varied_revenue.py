"""
Simple command to add varied revenue data to show real business patterns
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import random

from ems.models import Customer, CustomerInvoice, Employee


class Command(BaseCommand):
    help = 'Add varied revenue data to demonstrate real business patterns'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('💰 Adding Varied Revenue Patterns')
        )
        
        customers = list(Customer.objects.filter(status='active'))
        finance_employee = Employee.objects.filter(is_active=True).first()
        
        if not customers or not finance_employee:
            self.stdout.write(
                self.style.ERROR('❌ No customers or employees found')
            )
            return
        
        # Create varied revenue patterns for last 12 months
        created_count = 0
        
        for month_offset in range(12):
            invoice_date = timezone.now().date() - timedelta(days=30 * month_offset)
            month = invoice_date.month
            
            # Seasonal business patterns
            seasonal_patterns = {
                1: {'multiplier': 0.7, 'count': 2, 'description': 'Post-holiday slowdown'},
                2: {'multiplier': 0.8, 'count': 3, 'description': 'Winter recovery'},
                3: {'multiplier': 1.1, 'count': 4, 'description': 'Q1 push'},
                4: {'multiplier': 1.0, 'count': 4, 'description': 'Spring steady'},
                5: {'multiplier': 1.2, 'count': 5, 'description': 'Spring growth'},
                6: {'multiplier': 1.1, 'count': 4, 'description': 'Mid-year stable'},
                7: {'multiplier': 0.9, 'count': 3, 'description': 'Summer vacation'},
                8: {'multiplier': 0.8, 'count': 2, 'description': 'August slowdown'},
                9: {'multiplier': 1.3, 'count': 6, 'description': 'Back-to-business surge'},
                10: {'multiplier': 1.4, 'count': 7, 'description': 'Q4 acceleration'},
                11: {'multiplier': 1.6, 'count': 8, 'description': 'Holiday season boom'},
                12: {'multiplier': 1.8, 'count': 9, 'description': 'Year-end rush'}
            }
            
            pattern = seasonal_patterns.get(month, {'multiplier': 1.0, 'count': 4, 'description': 'Normal'})
            
            # Create invoices for this month
            for i in range(pattern['count']):
                customer = random.choice(customers)
                
                # Base amount with seasonal variation
                base_amounts = [75000, 120000, 200000, 350000, 500000, 800000]
                base_amount = random.choice(base_amounts)
                seasonal_amount = int(base_amount * pattern['multiplier'])
                
                # Add randomness for realistic variation
                variation = random.uniform(0.6, 1.4)
                final_amount = int(seasonal_amount * variation)
                
                subtotal = Decimal(str(final_amount))
                tax_amount = (subtotal * Decimal('0.15')).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount
                
                # Payment patterns vary by season and amount
                if pattern['multiplier'] > 1.2:  # High season
                    payment_prob = 0.95
                elif final_amount > 400000:  # Large invoices
                    payment_prob = 0.85
                else:
                    payment_prob = 0.90
                
                is_paid = random.random() < payment_prob
                
                # Create unique invoice number
                invoice_number = f"VAR-{invoice_date.strftime('%Y%m')}-{i+1:03d}-{random.randint(100, 999)}"
                
                try:
                    invoice = CustomerInvoice.objects.create(
                        customer=customer,
                        invoice_number=invoice_number,
                        invoice_date=invoice_date,
                        due_date=invoice_date + timedelta(days=30),
                        description=f"{pattern['description']} - {customer.company_name}",
                        subtotal=subtotal,
                        tax_amount=tax_amount,
                        total_amount=total_amount,
                        paid_amount=total_amount if is_paid else Decimal('0'),
                        status='PAID' if is_paid else random.choice(['SENT', 'PARTIAL']),
                        created_by=finance_employee
                    )
                    created_count += 1
                    
                except Exception as e:
                    # Skip if duplicate or other error
                    continue
            
            self.stdout.write(
                f'   📅 {invoice_date.strftime("%B %Y")}: {pattern["count"]} invoices '
                f'({pattern["description"]}, {pattern["multiplier"]}x multiplier)'
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Created {created_count} varied revenue records!')
        )
        
        # Show the impact
        self.show_revenue_summary()

    def show_revenue_summary(self):
        """Show revenue summary by month"""
        self.stdout.write('\n📊 Revenue Summary by Month:')
        self.stdout.write('-' * 50)
        
        # Get revenue by month for last 12 months
        monthly_revenue = {}
        
        for month_offset in range(12):
            start_date = timezone.now().date() - timedelta(days=30 * (month_offset + 1))
            end_date = timezone.now().date() - timedelta(days=30 * month_offset)
            
            month_revenue = CustomerInvoice.objects.filter(
                invoice_date__range=[start_date, end_date],
                status='PAID'
            ).aggregate(total=models.Sum('paid_amount'))['total'] or Decimal('0')
            
            month_name = start_date.strftime('%B %Y')
            monthly_revenue[month_name] = float(month_revenue)
        
        # Sort by date and display
        for month, revenue in monthly_revenue.items():
            self.stdout.write(f'   {month}: {revenue:,.0f} SAR')
        
        total_revenue = sum(monthly_revenue.values())
        avg_revenue = total_revenue / 12 if monthly_revenue else 0
        
        self.stdout.write(f'\n💰 Total Revenue (12 months): {total_revenue:,.0f} SAR')
        self.stdout.write(f'📊 Average Monthly Revenue: {avg_revenue:,.0f} SAR')
        
        # Show seasonal variation
        if monthly_revenue:
            max_month = max(monthly_revenue, key=monthly_revenue.get)
            min_month = min(monthly_revenue, key=monthly_revenue.get)
            
            self.stdout.write(f'📈 Highest Month: {max_month} ({monthly_revenue[max_month]:,.0f} SAR)')
            self.stdout.write(f'📉 Lowest Month: {min_month} ({monthly_revenue[min_month]:,.0f} SAR)')
            
            variation = (monthly_revenue[max_month] - monthly_revenue[min_month]) / monthly_revenue[min_month] * 100
            self.stdout.write(f'🔄 Seasonal Variation: {variation:.1f}%')


# Import models for aggregation
from django.db import models
