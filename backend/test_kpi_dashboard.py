#!/usr/bin/env python3
"""
Test script to demonstrate KPI Dashboard functionality
This shows how the real business data drives KPI calculations
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django
sys.path.append('/Users/<USER>/Desktop/EMS/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import KPIMetric, KPIMetricValue, Employee, Project, CustomerInvoice


def test_kpi_dashboard():
    """Test the KPI dashboard functionality"""
    print("🧪 Testing KPI Dashboard Functionality")
    print("=" * 50)
    
    # Test 1: Check current KPI data
    print("\n📊 Current KPI Data:")
    print("-" * 30)
    
    total_kpis = KPIMetric.objects.count()
    active_kpis = KPIMetric.objects.filter(is_active=True).count()
    total_values = KPIMetricValue.objects.count()
    
    print(f"Total KPI Metrics: {total_kpis}")
    print(f"Active KPI Metrics: {active_kpis}")
    print(f"Total KPI Values: {total_values}")
    
    # Test 2: Show recent KPI calculations
    print("\n📈 Recent KPI Calculations:")
    print("-" * 30)
    
    recent_kpis = KPIMetricValue.objects.order_by('-calculated_at')[:5]
    for kpi_value in recent_kpis:
        print(f"• {kpi_value.kpi_metric.name}: {kpi_value.value} "
              f"({kpi_value.calculated_at.strftime('%Y-%m-%d %H:%M')})")
    
    # Test 3: Show business data foundation
    print("\n🏢 Business Data Foundation:")
    print("-" * 30)
    
    employees = Employee.objects.filter(is_active=True).count()
    projects = Project.objects.count()
    active_projects = Project.objects.filter(status__in=['IN_PROGRESS', 'PLANNING']).count()
    invoices = CustomerInvoice.objects.count()
    
    print(f"Active Employees: {employees}")
    print(f"Total Projects: {projects}")
    print(f"Active Projects: {active_projects}")
    print(f"Customer Invoices: {invoices}")
    
    # Test 4: Calculate sample KPI
    print("\n🔄 Sample KPI Calculation:")
    print("-" * 30)
    
    # Calculate employee utilization
    if employees > 0 and active_projects > 0:
        utilization_rate = (active_projects / employees) * 100
        print(f"Employee Utilization Rate: {utilization_rate:.1f}%")
        print(f"Calculation: ({active_projects} active projects / {employees} employees) * 100")
    
    # Test 5: Show KPI categories
    print("\n📋 KPI Categories:")
    print("-" * 30)
    
    financial_kpis = KPIMetric.objects.filter(
        name__icontains='revenue'
    ).count() + KPIMetric.objects.filter(
        name__icontains='cash'
    ).count()
    
    hr_kpis = KPIMetric.objects.filter(
        name__icontains='employee'
    ).count() + KPIMetric.objects.filter(
        name__icontains='turnover'
    ).count()
    
    operational_kpis = KPIMetric.objects.filter(
        name__icontains='utilization'
    ).count() + KPIMetric.objects.filter(
        name__icontains='project'
    ).count()
    
    print(f"Financial KPIs: {financial_kpis}")
    print(f"HR KPIs: {hr_kpis}")
    print(f"Operational KPIs: {operational_kpis}")
    
    print("\n" + "=" * 50)
    print("✅ KPI Dashboard Test Complete!")
    
    return {
        'total_kpis': total_kpis,
        'active_kpis': active_kpis,
        'total_values': total_values,
        'employees': employees,
        'projects': projects,
        'active_projects': active_projects,
        'utilization_rate': utilization_rate if employees > 0 and active_projects > 0 else 0
    }


def demonstrate_api_endpoints():
    """Demonstrate the KPI Dashboard API endpoints"""
    print("\n🌐 KPI Dashboard API Endpoints:")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        {
            'name': 'Dashboard Overview',
            'url': f'{base_url}/api/kpi/dashboard/',
            'description': 'Complete KPI dashboard with trends and alerts'
        },
        {
            'name': 'Financial KPIs',
            'url': f'{base_url}/api/kpi/dashboard/financial/',
            'description': 'Financial performance indicators'
        },
        {
            'name': 'HR KPIs',
            'url': f'{base_url}/api/kpi/dashboard/hr/',
            'description': 'Human resources metrics'
        },
        {
            'name': 'Operational KPIs',
            'url': f'{base_url}/api/kpi/dashboard/operational/',
            'description': 'Operational efficiency metrics'
        },
        {
            'name': 'Customer KPIs',
            'url': f'{base_url}/api/kpi/dashboard/customer/',
            'description': 'Customer satisfaction and retention metrics'
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n📊 {endpoint['name']}:")
        print(f"   URL: {endpoint['url']}")
        print(f"   Description: {endpoint['description']}")
    
    print(f"\n📈 KPI Detail Chart:")
    print(f"   URL: {base_url}/api/kpi/chart/<kpi_id>/")
    print(f"   Description: Detailed chart data for specific KPI")
    
    print("\n💡 Usage Examples:")
    print("   curl -H 'Authorization: Bearer <token>' http://localhost:8000/api/kpi/dashboard/")
    print("   curl -H 'Authorization: Bearer <token>' http://localhost:8000/api/kpi/dashboard/financial/")


def show_management_commands():
    """Show available management commands"""
    print("\n⚙️ Available Management Commands:")
    print("=" * 50)
    
    commands = [
        {
            'command': 'python manage.py setup_real_business_data',
            'description': 'Create real business foundation data'
        },
        {
            'command': 'python manage.py create_real_operations_data',
            'description': 'Generate operational and financial data'
        },
        {
            'command': 'python manage.py calculate_real_kpis',
            'description': 'Calculate KPIs from real business data'
        },
        {
            'command': 'python manage.py expand_kpi_definitions',
            'description': 'Add comprehensive KPI definitions'
        },
        {
            'command': 'python manage.py expand_business_data',
            'description': 'Add more business scenarios and data'
        },
        {
            'command': 'python manage.py schedule_kpi_calculations',
            'description': 'Set up automated KPI scheduling'
        },
        {
            'command': 'python manage.py show_real_data_summary',
            'description': 'Display comprehensive data overview'
        },
        {
            'command': 'python manage.py setup_advanced_kpi_system',
            'description': 'Complete advanced KPI system setup'
        }
    ]
    
    for cmd in commands:
        print(f"\n🔧 {cmd['command']}")
        print(f"   {cmd['description']}")


def main():
    """Main test function"""
    print("🚀 KPI Dashboard System Demonstration")
    print("=" * 60)
    
    # Run tests
    test_results = test_kpi_dashboard()
    
    # Show API endpoints
    demonstrate_api_endpoints()
    
    # Show management commands
    show_management_commands()
    
    # Final summary
    print("\n🎯 SYSTEM CAPABILITIES:")
    print("=" * 50)
    print("✅ Real business data foundation")
    print("✅ Automatic KPI calculations")
    print("✅ RESTful API endpoints")
    print("✅ Comprehensive management commands")
    print("✅ Automated scheduling system")
    print("✅ Multi-category KPI support")
    print("✅ Real-time dashboard data")
    print("✅ Trend analysis and alerts")
    
    print(f"\n📊 Current System Status:")
    print(f"   • {test_results['total_kpis']} KPI metrics defined")
    print(f"   • {test_results['total_values']} KPI values calculated")
    print(f"   • {test_results['employees']} active employees")
    print(f"   • {test_results['active_projects']} active projects")
    print(f"   • {test_results['utilization_rate']:.1f}% utilization rate")
    
    print("\n🎉 KPI Dashboard System Ready for Production!")


if __name__ == '__main__':
    main()
