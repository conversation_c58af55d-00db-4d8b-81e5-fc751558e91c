"""
Management command to create real operational data
This creates actual business operations that will drive KPI calculations
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import random

from ems.models import (
    Employee, Department, Customer, Vendor, Project, Task, 
    Expense, CustomerInvoice, VendorInvoice, Payment,
    Attendance, LeaveRequest, LeaveType
)


class Command(BaseCommand):
    help = 'Create real operational data for KPI calculations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--months',
            type=int,
            default=6,
            help='Number of months of historical data to create'
        )
        parser.add_argument(
            '--projects',
            type=int,
            default=8,
            help='Number of projects to create'
        )

    def handle(self, *args, **options):
        self.months = options['months']
        self.project_count = options['projects']
        
        self.stdout.write(
            self.style.SUCCESS(f'🚀 Creating real operational data for {self.months} months...')
        )
        
        # Get existing data
        employees = list(Employee.objects.filter(is_active=True))
        customers = list(Customer.objects.filter(status='active'))
        vendors = list(Vendor.objects.filter(is_active=True))
        leave_types = list(LeaveType.objects.all())
        
        if not employees:
            self.stdout.write(
                self.style.ERROR('❌ No employees found. Run setup_real_business_data first.')
            )
            return
        
        # Create operational data
        projects = self.create_real_projects(employees, customers)
        tasks = self.create_real_tasks(projects, employees)
        invoices = self.create_real_customer_invoices(customers, employees)
        vendor_invoices = self.create_real_vendor_invoices(vendors, employees)
        expenses = self.create_real_expenses(employees)
        attendance = self.create_real_attendance(employees)
        leave_requests = self.create_real_leave_requests(employees, leave_types)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Real operational data created!\n'
                f'   📊 Projects: {len(projects)}\n'
                f'   📋 Tasks: {len(tasks)}\n'
                f'   💰 Customer Invoices: {len(invoices)}\n'
                f'   🧾 Vendor Invoices: {len(vendor_invoices)}\n'
                f'   💳 Expenses: {len(expenses)}\n'
                f'   ⏰ Attendance Records: {len(attendance)}\n'
                f'   🏖️ Leave Requests: {len(leave_requests)}'
            )
        )

    def create_real_projects(self, employees, customers):
        """Create actual business projects"""
        projects_data = [
            {
                'name': 'Enterprise Resource Planning System',
                'name_ar': 'نظام تخطيط موارد المؤسسة',
                'description': 'Complete ERP system implementation for large enterprise',
                'client': 'Saudi Aramco',
                'budget_amount': 2500000,
                'duration_days': 365,
                'status': 'IN_PROGRESS'
            },
            {
                'name': 'Digital Banking Platform',
                'name_ar': 'منصة الخدمات المصرفية الرقمية',
                'description': 'Modern digital banking solution with mobile app',
                'client': 'Al Rajhi Bank',
                'budget_amount': 1800000,
                'duration_days': 270,
                'status': 'IN_PROGRESS'
            },
            {
                'name': 'Smart City Infrastructure',
                'name_ar': 'البنية التحتية للمدينة الذكية',
                'description': 'IoT and smart systems for city management',
                'client': 'Riyadh Municipality',
                'budget_amount': 3200000,
                'duration_days': 450,
                'status': 'PLANNING'
            },
            {
                'name': 'E-Learning Management System',
                'name_ar': 'نظام إدارة التعلم الإلكتروني',
                'description': 'Comprehensive LMS for university education',
                'client': 'King Saud University',
                'budget_amount': 950000,
                'duration_days': 180,
                'status': 'IN_PROGRESS'
            },
            {
                'name': 'Supply Chain Optimization',
                'name_ar': 'تحسين سلسلة التوريد',
                'description': 'AI-powered supply chain management system',
                'client': 'Almarai Company',
                'budget_amount': 1200000,
                'duration_days': 210,
                'status': 'COMPLETED'
            },
            {
                'name': 'Telecommunications Network Upgrade',
                'name_ar': 'ترقية شبكة الاتصالات',
                'description': '5G network infrastructure implementation',
                'client': 'STC (Saudi Telecom)',
                'budget_amount': 4500000,
                'duration_days': 540,
                'status': 'IN_PROGRESS'
            },
            {
                'name': 'Financial Analytics Platform',
                'name_ar': 'منصة التحليلات المالية',
                'description': 'Advanced financial reporting and analytics',
                'client': 'SAMBA Financial Group',
                'budget_amount': 750000,
                'duration_days': 150,
                'status': 'COMPLETED'
            },
            {
                'name': 'Industrial IoT Solution',
                'name_ar': 'حل إنترنت الأشياء الصناعي',
                'description': 'IoT monitoring for industrial operations',
                'client': 'SABIC',
                'budget_amount': 1600000,
                'duration_days': 300,
                'status': 'IN_PROGRESS'
            }
        ]
        
        # Create client mapping (projects use client field, not customer)
        # We'll just use the client name as a string field
        
        # Get project managers (senior employees)
        managers = [emp for emp in employees if 'Manager' in emp.position or 'Officer' in emp.position]
        if not managers:
            managers = employees[:3]  # Use first 3 employees as fallback
        
        projects = []
        for i, proj_data in enumerate(projects_data[:self.project_count]):
            start_date = timezone.now().date() - timedelta(days=random.randint(30, 180))
            end_date = start_date + timedelta(days=proj_data['duration_days'])

            project, created = Project.objects.get_or_create(
                name=proj_data['name'],
                defaults={
                    'name_ar': proj_data['name_ar'],
                    'description': proj_data['description'],
                    'description_ar': proj_data['description'],
                    'client': proj_data['client'],
                    'project_manager': random.choice(managers),
                    'start_date': start_date,
                    'end_date': end_date,
                    'budget_amount': Decimal(str(proj_data['budget_amount'])),
                    'status': proj_data['status'],
                    'priority': random.choice(['HIGH', 'MEDIUM', 'LOW'])
                }
            )
            
            projects.append(project)
            if created:
                self.stdout.write(f'  ✅ Created project: {project.name} (Budget: {project.budget_amount:,} SAR)')
        
        return projects

    def create_real_tasks(self, projects, employees):
        """Create actual project tasks"""
        task_templates = [
            'Requirements Analysis', 'System Design', 'Database Design', 'Frontend Development',
            'Backend Development', 'API Integration', 'Testing & QA', 'User Training',
            'Documentation', 'Deployment', 'Security Review', 'Performance Optimization'
        ]
        
        tasks = []
        for project in projects:
            # Create 8-15 tasks per project
            num_tasks = random.randint(8, 15)
            project_employees = random.sample(employees, min(len(employees), 6))
            
            for i in range(num_tasks):
                task_name = f"{random.choice(task_templates)} - {project.name[:30]}"
                
                # Calculate task dates within project timeline
                task_start = project.start_date + timedelta(days=random.randint(0, 60))
                task_duration = random.randint(5, 30)
                task_end = task_start + timedelta(days=task_duration)
                
                # Determine task status based on dates
                today = timezone.now().date()
                if task_end < today:
                    status = random.choice(['COMPLETED', 'COMPLETED', 'COMPLETED', 'CANCELLED'])
                elif task_start <= today <= task_end:
                    status = random.choice(['IN_PROGRESS', 'IN_PROGRESS', 'ON_HOLD'])
                else:
                    status = 'NOT_STARTED'
                
                task = Task.objects.create(
                    project=project,
                    title=task_name,
                    title_ar=f"مهمة {i+1} - {project.name_ar[:30] if project.name_ar else project.name[:30]}",
                    description=f"Task description for {task_name}",
                    description_ar=f"وصف المهمة لـ {task_name}",
                    assigned_to=random.choice(project_employees),
                    created_by=project.project_manager,
                    start_date=task_start,
                    due_date=task_end,
                    completion_date=task_end if status == 'COMPLETED' else None,
                    status=status,
                    priority=random.choice(['HIGH', 'MEDIUM', 'LOW']),
                    estimated_hours=Decimal(str(random.randint(20, 80)))
                )
                
                tasks.append(task)
        
        self.stdout.write(f'  ✅ Created {len(tasks)} project tasks')
        return tasks

    def create_real_customer_invoices(self, customers, employees):
        """Create actual customer invoices"""
        invoices = []
        finance_employee = next((emp for emp in employees if emp.department and 'Finance' in emp.department.name), employees[0])

        # Create monthly invoices for the past months
        for month_offset in range(self.months):
            invoice_date = timezone.now().date() - timedelta(days=30 * month_offset)

            # Create 2-4 invoices per month
            for _ in range(random.randint(2, 4)):
                customer = random.choice(customers)

                # Generate realistic invoice amounts
                base_amount = random.randint(50000, 500000)
                subtotal = Decimal(str(base_amount))
                tax_rate = Decimal('0.15')  # 15% VAT
                tax_amount = (subtotal * tax_rate).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount

                due_date = invoice_date + timedelta(days=30)

                # Determine payment status
                if invoice_date < timezone.now().date() - timedelta(days=45):
                    status = 'PAID'
                    paid_amount = total_amount
                elif invoice_date < timezone.now().date() - timedelta(days=15):
                    status = random.choice(['PAID', 'PARTIAL', 'SENT'])
                    paid_amount = total_amount if status == 'PAID' else (total_amount * Decimal('0.5') if status == 'PARTIAL' else Decimal('0'))
                else:
                    status = 'SENT'
                    paid_amount = Decimal('0')

                invoice = CustomerInvoice.objects.create(
                    customer=customer,
                    invoice_number=f"INV-{invoice_date.strftime('%Y%m')}-{len(invoices) + 1:04d}",
                    invoice_date=invoice_date,
                    due_date=due_date,
                    description=f"Professional services for {customer.company_name}",
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    status=status,
                    created_by=finance_employee
                )

                invoices.append(invoice)

        self.stdout.write(f'  ✅ Created {len(invoices)} customer invoices')
        return invoices

    def create_real_vendor_invoices(self, vendors, employees):
        """Create actual vendor invoices"""
        invoices = []
        finance_employee = next((emp for emp in employees if emp.department and 'Finance' in emp.department.name), employees[0])

        # Create vendor invoices for operational expenses
        for month_offset in range(self.months):
            invoice_date = timezone.now().date() - timedelta(days=30 * month_offset)

            # Create 1-3 vendor invoices per month
            for _ in range(random.randint(1, 3)):
                vendor = random.choice(vendors)

                # Generate realistic vendor invoice amounts
                base_amount = random.randint(10000, 150000)
                subtotal = Decimal(str(base_amount))
                tax_rate = Decimal('0.15')
                tax_amount = (subtotal * tax_rate).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount

                due_date = invoice_date + timedelta(days=30)

                # Most vendor invoices should be paid
                status = random.choice(['PAID', 'PAID', 'PAID', 'APPROVED', 'PENDING'])
                paid_amount = total_amount if status == 'PAID' else Decimal('0')

                invoice = VendorInvoice.objects.create(
                    vendor=vendor,
                    invoice_number=f"VINV-{vendor.id}-{invoice_date.strftime('%Y%m')}-{len(invoices) + 1:03d}",
                    vendor_invoice_number=f"V{vendor.id}-{len(invoices) + 1:04d}",
                    invoice_date=invoice_date,
                    due_date=due_date,
                    description=f"Technology services from {vendor.company_name}",
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    status=status,
                    created_by=finance_employee
                )

                invoices.append(invoice)

        self.stdout.write(f'  ✅ Created {len(invoices)} vendor invoices')
        return invoices

    def create_real_expenses(self, employees):
        """Create actual employee expenses"""
        expenses = []
        expense_categories = ['TRAVEL', 'OFFICE_SUPPLIES', 'TRAINING', 'MEALS', 'TRANSPORTATION', 'EQUIPMENT']

        # Create expenses for each employee over the months
        for employee in employees:
            # Senior employees have more expenses
            monthly_expenses = 3 if 'Manager' in employee.position or 'Officer' in employee.position else 1

            for month_offset in range(self.months):
                for _ in range(random.randint(0, monthly_expenses)):
                    expense_date = timezone.now().date() - timedelta(days=30 * month_offset + random.randint(0, 28))

                    category = random.choice(expense_categories)
                    amount = Decimal(str(random.randint(100, 2000)))

                    # Generate realistic expense descriptions
                    descriptions = {
                        'TRAVEL': 'Business travel expenses',
                        'OFFICE_SUPPLIES': 'Office supplies and materials',
                        'TRAINING': 'Professional development training',
                        'MEALS': 'Business meal expenses',
                        'TRANSPORTATION': 'Transportation costs',
                        'EQUIPMENT': 'Equipment and tools'
                    }

                    expense = Expense.objects.create(
                        employee=employee,
                        title=f"{descriptions[category]} - {expense_date.strftime('%B %Y')}",
                        title_ar=f"مصروف {category} - {expense_date.strftime('%B %Y')}",
                        description=f"Business expense for {descriptions[category].lower()}",
                        description_ar=f"مصروف عمل لـ {descriptions[category]}",
                        category=category,
                        amount=amount,
                        currency='SAR',
                        expense_date=expense_date,
                        status=random.choice(['APPROVED', 'APPROVED', 'APPROVED', 'PENDING'])
                    )

                    expenses.append(expense)

        self.stdout.write(f'  ✅ Created {len(expenses)} employee expenses')
        return expenses

    def create_real_attendance(self, employees):
        """Create actual attendance records"""
        attendance_records = []

        # Create attendance for the past months (working days only)
        start_date = timezone.now().date() - timedelta(days=30 * self.months)
        end_date = timezone.now().date()

        current_date = start_date
        while current_date <= end_date:
            # Skip weekends (Friday and Saturday in Saudi Arabia)
            if current_date.weekday() not in [4, 5]:  # 4=Friday, 5=Saturday

                for employee in employees:
                    # 95% attendance rate (realistic)
                    if random.random() < 0.95:
                        # Normal working hours: 8:00 AM to 5:00 PM
                        check_in_hour = random.randint(7, 9)  # 7-9 AM
                        check_in_minute = random.randint(0, 59)

                        check_out_hour = random.randint(16, 18)  # 4-6 PM
                        check_out_minute = random.randint(0, 59)

                        check_in_time = timezone.make_aware(
                            datetime.combine(current_date, datetime.min.time().replace(
                                hour=check_in_hour, minute=check_in_minute
                            ))
                        )

                        check_out_time = timezone.make_aware(
                            datetime.combine(current_date, datetime.min.time().replace(
                                hour=check_out_hour, minute=check_out_minute
                            ))
                        )

                        # Calculate hours worked
                        hours_worked = (check_out_time - check_in_time).total_seconds() / 3600

                        attendance = Attendance.objects.create(
                            employee=employee,
                            date=current_date,
                            check_in=check_in_time,
                            check_out=check_out_time,
                            hours_worked=Decimal(str(round(hours_worked, 2))),
                            status='PRESENT',
                            notes=f"Regular attendance for {current_date}"
                        )

                        attendance_records.append(attendance)

            current_date += timedelta(days=1)

        self.stdout.write(f'  ✅ Created {len(attendance_records)} attendance records')
        return attendance_records

    def create_real_leave_requests(self, employees, leave_types):
        """Create actual leave requests"""
        leave_requests = []

        # Create realistic leave requests
        for employee in employees:
            # Each employee takes 1-3 leaves over the period
            num_leaves = random.randint(1, 3)

            for _ in range(num_leaves):
                leave_type = random.choice(leave_types)

                # Random leave date in the past months
                leave_start = timezone.now().date() - timedelta(days=random.randint(7, 30 * self.months))

                # Leave duration based on type
                if leave_type.name == 'Annual Leave':
                    duration = random.randint(3, 14)  # 3-14 days
                elif leave_type.name == 'Sick Leave':
                    duration = random.randint(1, 5)   # 1-5 days
                elif leave_type.name == 'Emergency Leave':
                    duration = random.randint(1, 3)   # 1-3 days
                else:
                    duration = random.randint(1, 7)   # 1-7 days

                leave_end = leave_start + timedelta(days=duration - 1)

                # Most leaves should be approved
                status = random.choice(['APPROVED', 'APPROVED', 'APPROVED', 'PENDING', 'REJECTED'])

                leave_request = LeaveRequest.objects.create(
                    employee=employee,
                    leave_type=leave_type,
                    start_date=leave_start,
                    end_date=leave_end,
                    days_requested=duration,
                    reason=f"{leave_type.name} request for personal reasons",
                    status=status,
                    applied_date=leave_start - timedelta(days=random.randint(1, 14))
                )

                leave_requests.append(leave_request)

        self.stdout.write(f'  ✅ Created {len(leave_requests)} leave requests')
        return leave_requests
