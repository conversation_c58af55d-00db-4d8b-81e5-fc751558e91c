"""
Management command to expand business data using established patterns
This adds more realistic business scenarios and data volume
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import random

from ems.models import (
    Employee, Department, Customer, Vendor, Project, Task, 
    Expense, CustomerInvoice, VendorInvoice, Attendance, 
    LeaveRequest, LeaveType, KPIMetric
)


class Command(BaseCommand):
    help = 'Expand business data using established patterns'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario',
            type=str,
            choices=['growth', 'seasonal', 'crisis', 'expansion'],
            default='growth',
            help='Business scenario to simulate'
        )
        parser.add_argument(
            '--months',
            type=int,
            default=6,
            help='Number of months of additional data'
        )
        parser.add_argument(
            '--scale',
            type=float,
            default=1.0,
            help='Scale factor for data volume (1.0 = normal, 2.0 = double)'
        )

    def handle(self, *args, **options):
        scenario = options['scenario']
        months = options['months']
        scale = options['scale']
        
        self.stdout.write(
            self.style.SUCCESS(f'📈 Expanding business data - {scenario.upper()} scenario')
        )
        
        if scenario == 'growth':
            self.simulate_business_growth(months, scale)
        elif scenario == 'seasonal':
            self.simulate_seasonal_patterns(months, scale)
        elif scenario == 'crisis':
            self.simulate_crisis_scenario(months, scale)
        elif scenario == 'expansion':
            self.simulate_business_expansion(months, scale)

    def simulate_business_growth(self, months, scale):
        """Simulate steady business growth"""
        self.stdout.write('🚀 Simulating business growth scenario...')
        
        # Add new employees (growth pattern)
        new_employees = self.add_growth_employees(int(5 * scale))
        
        # Add new projects (increasing complexity)
        new_projects = self.add_growth_projects(int(3 * scale), months)
        
        # Add seasonal revenue patterns
        self.add_growth_revenue_pattern(months, scale)
        
        # Add performance improvements
        self.add_performance_improvements(months)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Growth scenario complete:\n'
                f'   👥 New Employees: {len(new_employees)}\n'
                f'   📊 New Projects: {len(new_projects)}\n'
                f'   📈 Revenue Growth: {months} months\n'
                f'   🎯 Performance Improvements: Applied'
            )
        )

    def simulate_seasonal_patterns(self, months, scale):
        """Simulate seasonal business patterns"""
        self.stdout.write('🌟 Simulating seasonal business patterns...')
        
        # Q4 holiday season boost
        self.add_seasonal_revenue_boost(months, scale)
        
        # Summer vacation period
        self.add_vacation_period_data(months)
        
        # Year-end project rush
        self.add_year_end_projects(int(2 * scale))
        
        self.stdout.write(
            self.style.SUCCESS('✅ Seasonal patterns applied')
        )

    def simulate_crisis_scenario(self, months, scale):
        """Simulate business crisis and recovery"""
        self.stdout.write('⚠️ Simulating crisis and recovery scenario...')
        
        # Reduced operations
        self.add_crisis_period_data(months // 2)
        
        # Recovery phase
        self.add_recovery_phase_data(months // 2, scale)
        
        self.stdout.write(
            self.style.SUCCESS('✅ Crisis and recovery scenario complete')
        )

    def simulate_business_expansion(self, months, scale):
        """Simulate major business expansion"""
        self.stdout.write('🏢 Simulating business expansion scenario...')
        
        # New departments
        new_departments = self.add_expansion_departments()
        
        # Major new projects
        new_projects = self.add_expansion_projects(int(5 * scale))
        
        # New customer segments
        new_customers = self.add_expansion_customers(int(8 * scale))
        
        # Infrastructure investments
        self.add_infrastructure_investments(months, scale)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Expansion scenario complete:\n'
                f'   🏬 New Departments: {len(new_departments)}\n'
                f'   📊 Major Projects: {len(new_projects)}\n'
                f'   🤝 New Customers: {len(new_customers)}\n'
                f'   🏗️ Infrastructure: Upgraded'
            )
        )

    def add_growth_employees(self, count):
        """Add employees following growth pattern"""
        employees = []
        departments = list(Department.objects.filter(is_active=True))
        
        growth_positions = [
            {'title': 'Senior Data Analyst', 'title_ar': 'محلل بيانات أول', 'salary': 15000},
            {'title': 'DevOps Engineer', 'title_ar': 'مهندس DevOps', 'salary': 18000},
            {'title': 'Product Manager', 'title_ar': 'مدير منتج', 'salary': 20000},
            {'title': 'UX Designer', 'title_ar': 'مصمم تجربة مستخدم', 'salary': 12000},
            {'title': 'Business Analyst', 'title_ar': 'محلل أعمال', 'salary': 14000},
            {'title': 'Quality Assurance Lead', 'title_ar': 'قائد ضمان الجودة', 'salary': 16000},
            {'title': 'Cloud Architect', 'title_ar': 'مهندس معماري سحابي', 'salary': 25000},
        ]
        
        for i in range(count):
            position = random.choice(growth_positions)
            department = random.choice(departments)
            
            # Create user
            username = f"growth_emp_{i+1}"
            from django.contrib.auth.models import User
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'first_name': f'Employee{i+1}',
                    'last_name': 'Growth',
                    'email': f'{username}@techcorp.sa'
                }
            )
            
            if created:
                employee = Employee.objects.create(
                    user=user,
                    employee_id=f"GROW{i+1:04d}",
                    position=position['title'],
                    position_ar=position['title_ar'],
                    department=department,
                    hire_date=timezone.now().date() - timedelta(days=random.randint(30, 180)),
                    salary=Decimal(str(position['salary'])),
                    phone=f"+966501{i+1000:04d}",
                    is_active=True,
                    employment_status='FULL_TIME',
                    gender='M'
                )
                employees.append(employee)
        
        return employees

    def add_growth_projects(self, count, months):
        """Add projects following growth pattern"""
        projects = []
        employees = list(Employee.objects.filter(is_active=True))
        managers = [emp for emp in employees if 'Manager' in emp.position or 'Lead' in emp.position]
        
        if not managers:
            managers = employees[:5]  # Fallback
        
        growth_projects = [
            {'name': 'AI-Powered Analytics Platform', 'budget': 3500000, 'duration': 365},
            {'name': 'Mobile App Development', 'budget': 1200000, 'duration': 180},
            {'name': 'Cloud Migration Initiative', 'budget': 2800000, 'duration': 270},
            {'name': 'Customer Portal Enhancement', 'budget': 800000, 'duration': 120},
            {'name': 'Data Warehouse Modernization', 'budget': 2200000, 'duration': 300},
        ]
        
        for i in range(min(count, len(growth_projects))):
            proj_data = growth_projects[i]
            
            start_date = timezone.now().date() - timedelta(days=random.randint(0, 90))
            end_date = start_date + timedelta(days=proj_data['duration'])
            
            project = Project.objects.create(
                name=proj_data['name'],
                name_ar=f"مشروع النمو {i+1}",
                description=f"Growth initiative project: {proj_data['name']}",
                client='Internal Growth Initiative',
                project_manager=random.choice(managers),
                start_date=start_date,
                end_date=end_date,
                budget_amount=Decimal(str(proj_data['budget'])),
                status=random.choice(['PLANNING', 'IN_PROGRESS']),
                priority='HIGH'
            )
            projects.append(project)
        
        return projects

    def add_growth_revenue_pattern(self, months, scale):
        """Add revenue growth pattern"""
        customers = list(Customer.objects.filter(status='active'))
        finance_employee = Employee.objects.filter(is_active=True).first()
        
        for month_offset in range(months):
            # Growth factor increases over time
            growth_factor = 1.0 + (month_offset * 0.1 * scale)
            
            invoice_date = timezone.now().date() - timedelta(days=30 * month_offset)
            
            # Create 3-6 invoices per month with growth
            for _ in range(random.randint(3, 6)):
                customer = random.choice(customers)
                
                base_amount = random.randint(80000, 600000)
                subtotal = Decimal(str(int(base_amount * growth_factor)))
                tax_amount = (subtotal * Decimal('0.15')).quantize(Decimal('0.01'))
                total_amount = subtotal + tax_amount
                
                CustomerInvoice.objects.create(
                    customer=customer,
                    invoice_number=f"GROW-{invoice_date.strftime('%Y%m')}-{random.randint(1000, 9999)}",
                    invoice_date=invoice_date,
                    due_date=invoice_date + timedelta(days=30),
                    description=f"Growth period services for {customer.company_name}",
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    paid_amount=total_amount if random.random() > 0.2 else Decimal('0'),
                    status='PAID' if random.random() > 0.2 else 'SENT',
                    created_by=finance_employee
                )

    def add_performance_improvements(self, months):
        """Add performance improvement indicators"""
        # This would typically involve updating existing records
        # to show improved metrics over time
        pass

    def add_seasonal_revenue_boost(self, months, scale):
        """Add Q4 seasonal revenue boost"""
        # Implementation for seasonal patterns
        pass

    def add_vacation_period_data(self, months):
        """Add summer vacation period data"""
        # Implementation for vacation patterns
        pass

    def add_year_end_projects(self, count):
        """Add year-end rush projects"""
        # Implementation for year-end projects
        pass

    def add_crisis_period_data(self, months):
        """Add crisis period data (reduced activity)"""
        # Implementation for crisis simulation
        pass

    def add_recovery_phase_data(self, months, scale):
        """Add recovery phase data"""
        # Implementation for recovery simulation
        pass

    def add_expansion_departments(self):
        """Add new departments for expansion"""
        new_departments = [
            {'name': 'Research & Development', 'name_ar': 'البحث والتطوير', 'budget': 1500000},
            {'name': 'International Business', 'name_ar': 'الأعمال الدولية', 'budget': 800000},
            {'name': 'Digital Innovation', 'name_ar': 'الابتكار الرقمي', 'budget': 1200000},
        ]
        
        departments = []
        for dept_data in new_departments:
            department, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults={
                    'name_ar': dept_data['name_ar'],
                    'budget_amount': Decimal(str(dept_data['budget'])),
                    'is_active': True
                }
            )
            if created:
                departments.append(department)
        
        return departments

    def add_expansion_projects(self, count):
        """Add major expansion projects"""
        # Implementation for expansion projects
        return []

    def add_expansion_customers(self, count):
        """Add new customer segments"""
        # Implementation for new customers
        return []

    def add_infrastructure_investments(self, months, scale):
        """Add infrastructure investment data"""
        # Implementation for infrastructure investments
        pass
