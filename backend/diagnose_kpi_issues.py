#!/usr/bin/env python3
"""
Diagnostic script to identify KPI and data issues
This will find bugs, inconsistencies, and problems in the system
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
sys.path.append('/Users/<USER>/Desktop/EMS/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import *
from django.db.models import Sum, Count, Avg, Max, Min
from django.utils import timezone


def diagnose_all_issues():
    """Comprehensive diagnostic of all KPI and data issues"""
    print("🔍 COMPREHENSIVE KPI & DATA DIAGNOSTIC")
    print("=" * 60)
    
    issues_found = []
    
    # 1. Check KPI calculation accuracy
    issues_found.extend(check_kpi_accuracy())
    
    # 2. Check data consistency
    issues_found.extend(check_data_consistency())
    
    # 3. Check missing or null data
    issues_found.extend(check_missing_data())
    
    # 4. Check KPI logic errors
    issues_found.extend(check_kpi_logic_errors())
    
    # 5. Check data relationships
    issues_found.extend(check_data_relationships())
    
    # 6. Check performance issues
    issues_found.extend(check_performance_issues())
    
    # Summary
    print(f"\n🚨 ISSUES SUMMARY:")
    print("=" * 40)
    
    if issues_found:
        print(f"❌ Found {len(issues_found)} issues:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
    else:
        print("✅ No issues found!")
    
    return issues_found


def check_kpi_accuracy():
    """Check KPI calculation accuracy against source data"""
    print("\n🎯 CHECKING KPI ACCURACY")
    print("-" * 40)
    
    issues = []
    
    # Check Revenue KPI accuracy
    revenue_kpi = KPIMetric.objects.filter(name__icontains='revenue').first()
    if revenue_kpi:
        latest_kpi_value = KPIMetricValue.objects.filter(
            kpi_metric=revenue_kpi
        ).order_by('-calculated_at').first()
        
        if latest_kpi_value:
            # Calculate actual revenue for the same period
            actual_revenue = CustomerInvoice.objects.filter(
                invoice_date__range=[latest_kpi_value.period_start, latest_kpi_value.period_end],
                status='PAID'
            ).aggregate(total=Sum('paid_amount'))['total'] or 0
            
            kpi_revenue = float(latest_kpi_value.value)
            actual_revenue_float = float(actual_revenue)
            
            difference = abs(kpi_revenue - actual_revenue_float)
            if difference > 1:  # Allow for rounding
                issues.append(f"Revenue KPI mismatch: KPI={kpi_revenue:,.0f}, Actual={actual_revenue_float:,.0f}, Diff={difference:,.0f}")
                print(f"❌ Revenue KPI inaccurate: {difference:,.0f} SAR difference")
            else:
                print(f"✅ Revenue KPI accurate: {kpi_revenue:,.0f} SAR")
    
    # Check for KPIs with zero values (suspicious)
    zero_kpis = KPIMetricValue.objects.filter(value=0).values('kpi_metric__name').annotate(count=Count('id'))
    for zero_kpi in zero_kpis:
        if zero_kpi['count'] > 5:  # More than 5 zero values is suspicious
            issues.append(f"KPI '{zero_kpi['kpi_metric__name']}' has {zero_kpi['count']} zero values")
            print(f"⚠️ {zero_kpi['kpi_metric__name']}: {zero_kpi['count']} zero values")
    
    # Check for KPIs with identical values (no variation)
    for kpi in KPIMetric.objects.filter(is_active=True):
        values = KPIMetricValue.objects.filter(kpi_metric=kpi).values_list('value', flat=True)
        unique_values = set(values)
        if len(values) > 3 and len(unique_values) == 1:
            issues.append(f"KPI '{kpi.name}' has no variation - all values are {list(unique_values)[0]}")
            print(f"⚠️ {kpi.name}: No variation in values")
    
    return issues


def check_data_consistency():
    """Check for data consistency issues"""
    print("\n📊 CHECKING DATA CONSISTENCY")
    print("-" * 40)
    
    issues = []
    
    # Check invoice totals
    invoices_with_wrong_totals = CustomerInvoice.objects.exclude(
        total_amount=models.F('subtotal') + models.F('tax_amount')
    )
    if invoices_with_wrong_totals.exists():
        count = invoices_with_wrong_totals.count()
        issues.append(f"{count} customer invoices have incorrect total calculations")
        print(f"❌ {count} invoices with wrong totals")
    else:
        print("✅ Invoice totals are correct")
    
    # Check paid amounts vs status
    paid_invoices_with_zero = CustomerInvoice.objects.filter(
        status='PAID',
        paid_amount=0
    )
    if paid_invoices_with_zero.exists():
        count = paid_invoices_with_zero.count()
        issues.append(f"{count} invoices marked as PAID but have zero paid amount")
        print(f"❌ {count} PAID invoices with zero amount")
    
    unpaid_invoices_with_amount = CustomerInvoice.objects.filter(
        status__in=['SENT', 'PENDING'],
        paid_amount__gt=0
    )
    if unpaid_invoices_with_amount.exists():
        count = unpaid_invoices_with_amount.count()
        issues.append(f"{count} unpaid invoices have paid amounts")
        print(f"❌ {count} unpaid invoices with amounts")
    
    # Check project dates
    projects_with_wrong_dates = Project.objects.filter(
        end_date__lt=models.F('start_date')
    )
    if projects_with_wrong_dates.exists():
        count = projects_with_wrong_dates.count()
        issues.append(f"{count} projects have end date before start date")
        print(f"❌ {count} projects with invalid dates")
    
    return issues


def check_missing_data():
    """Check for missing or null data"""
    print("\n🔍 CHECKING MISSING DATA")
    print("-" * 40)
    
    issues = []
    
    # Check employees without departments
    employees_no_dept = Employee.objects.filter(department=None, is_active=True)
    if employees_no_dept.exists():
        count = employees_no_dept.count()
        issues.append(f"{count} active employees have no department assigned")
        print(f"❌ {count} employees without departments")
        for emp in employees_no_dept[:3]:
            print(f"   • {emp.user.get_full_name()} (ID: {emp.employee_id})")
    
    # Check projects without managers
    projects_no_manager = Project.objects.filter(project_manager=None)
    if projects_no_manager.exists():
        count = projects_no_manager.count()
        issues.append(f"{count} projects have no project manager")
        print(f"❌ {count} projects without managers")
    
    # Check KPIs without target values
    kpis_no_target = KPIMetric.objects.filter(target_value=None, is_active=True)
    if kpis_no_target.exists():
        count = kpis_no_target.count()
        issues.append(f"{count} active KPIs have no target values")
        print(f"⚠️ {count} KPIs without targets")
        for kpi in kpis_no_target:
            print(f"   • {kpi.name}")
    
    # Check for empty descriptions
    invoices_no_desc = CustomerInvoice.objects.filter(
        models.Q(description='') | models.Q(description=None)
    )
    if invoices_no_desc.exists():
        count = invoices_no_desc.count()
        issues.append(f"{count} invoices have no description")
        print(f"⚠️ {count} invoices without descriptions")
    
    return issues


def check_kpi_logic_errors():
    """Check for KPI calculation logic errors"""
    print("\n🧮 CHECKING KPI LOGIC")
    print("-" * 40)
    
    issues = []
    
    # Check for negative values where they shouldn't be
    negative_revenue = KPIMetricValue.objects.filter(
        kpi_metric__name__icontains='revenue',
        value__lt=0
    )
    if negative_revenue.exists():
        count = negative_revenue.count()
        issues.append(f"{count} revenue KPI values are negative")
        print(f"❌ {count} negative revenue values")
    
    # Check for unrealistic percentages
    percentage_kpis = KPIMetricValue.objects.filter(
        kpi_metric__unit='%',
        value__gt=100
    )
    if percentage_kpis.exists():
        count = percentage_kpis.count()
        issues.append(f"{count} percentage KPIs exceed 100%")
        print(f"❌ {count} percentage values > 100%")
        for kpi_val in percentage_kpis[:3]:
            print(f"   • {kpi_val.kpi_metric.name}: {kpi_val.value}%")
    
    # Check for KPIs with no recent calculations
    stale_threshold = timezone.now() - timedelta(days=7)
    stale_kpis = []
    for kpi in KPIMetric.objects.filter(is_active=True):
        latest_calc = KPIMetricValue.objects.filter(
            kpi_metric=kpi
        ).order_by('-calculated_at').first()
        
        if not latest_calc or latest_calc.calculated_at < stale_threshold:
            stale_kpis.append(kpi.name)
    
    if stale_kpis:
        issues.append(f"{len(stale_kpis)} KPIs have no recent calculations")
        print(f"⚠️ {len(stale_kpis)} stale KPIs:")
        for kpi_name in stale_kpis[:3]:
            print(f"   • {kpi_name}")
    
    return issues


def check_data_relationships():
    """Check for broken data relationships"""
    print("\n🔗 CHECKING DATA RELATIONSHIPS")
    print("-" * 40)
    
    issues = []
    
    # Check for orphaned records
    try:
        # Tasks without projects
        orphaned_tasks = Task.objects.filter(project=None)
        if orphaned_tasks.exists():
            count = orphaned_tasks.count()
            issues.append(f"{count} tasks have no project assigned")
            print(f"❌ {count} orphaned tasks")
        
        # Expenses without employees
        orphaned_expenses = Expense.objects.filter(employee=None)
        if orphaned_expenses.exists():
            count = orphaned_expenses.count()
            issues.append(f"{count} expenses have no employee assigned")
            print(f"❌ {count} orphaned expenses")
        
        # Invoices without customers
        orphaned_invoices = CustomerInvoice.objects.filter(customer=None)
        if orphaned_invoices.exists():
            count = orphaned_invoices.count()
            issues.append(f"{count} invoices have no customer assigned")
            print(f"❌ {count} orphaned invoices")
    
    except Exception as e:
        issues.append(f"Error checking relationships: {str(e)}")
        print(f"❌ Relationship check error: {e}")
    
    return issues


def check_performance_issues():
    """Check for performance-related issues"""
    print("\n⚡ CHECKING PERFORMANCE ISSUES")
    print("-" * 40)
    
    issues = []
    
    # Check for duplicate KPI calculations
    duplicate_kpis = KPIMetricValue.objects.values(
        'kpi_metric', 'period_start', 'period_end'
    ).annotate(count=Count('id')).filter(count__gt=1)
    
    if duplicate_kpis.exists():
        count = duplicate_kpis.count()
        issues.append(f"{count} duplicate KPI calculations found")
        print(f"⚠️ {count} duplicate KPI calculations")
    
    # Check for very large KPI values (potential calculation errors)
    large_values = KPIMetricValue.objects.filter(value__gt=1000000000)  # > 1 billion
    if large_values.exists():
        count = large_values.count()
        issues.append(f"{count} KPI values are suspiciously large (>1B)")
        print(f"⚠️ {count} very large KPI values")
        for val in large_values[:3]:
            print(f"   • {val.kpi_metric.name}: {val.value:,.0f}")
    
    # Check database size
    total_records = (
        Employee.objects.count() +
        CustomerInvoice.objects.count() +
        Project.objects.count() +
        KPIMetricValue.objects.count() +
        Task.objects.count() +
        Expense.objects.count()
    )
    
    print(f"✅ Total database records: {total_records}")
    
    if total_records < 100:
        issues.append(f"Database has insufficient data: only {total_records} records")
        print(f"⚠️ Low data volume: {total_records} records")
    
    return issues


def show_detailed_kpi_analysis():
    """Show detailed analysis of each KPI"""
    print(f"\n📈 DETAILED KPI ANALYSIS")
    print("=" * 50)
    
    for kpi in KPIMetric.objects.filter(is_active=True):
        print(f"\n🎯 {kpi.name}")
        print("-" * 30)
        
        values = KPIMetricValue.objects.filter(kpi_metric=kpi).order_by('-calculated_at')
        
        if values.exists():
            latest = values.first()
            count = values.count()
            
            # Calculate statistics
            value_list = [float(v.value) for v in values]
            avg_value = sum(value_list) / len(value_list)
            min_value = min(value_list)
            max_value = max(value_list)
            
            print(f"   Latest Value: {latest.value} {kpi.unit or ''}")
            print(f"   Total Calculations: {count}")
            print(f"   Average: {avg_value:.2f}")
            print(f"   Range: {min_value:.2f} - {max_value:.2f}")
            print(f"   Last Calculated: {latest.calculated_at.strftime('%Y-%m-%d %H:%M')}")
            print(f"   Data Source: {latest.data_source}")
            
            # Check for issues
            if len(set(value_list)) == 1 and count > 3:
                print(f"   ⚠️ WARNING: No variation in values")
            
            if latest.value == 0:
                print(f"   ⚠️ WARNING: Latest value is zero")
            
            if kpi.target_value and abs(float(latest.value) - float(kpi.target_value)) / float(kpi.target_value) > 0.5:
                print(f"   ⚠️ WARNING: Far from target ({kpi.target_value})")
        else:
            print(f"   ❌ ERROR: No calculated values found")


if __name__ == '__main__':
    # Run comprehensive diagnostic
    issues = diagnose_all_issues()
    
    # Show detailed KPI analysis
    show_detailed_kpi_analysis()
    
    # Final recommendations
    print(f"\n🔧 RECOMMENDED FIXES:")
    print("=" * 40)
    
    if issues:
        print("1. Run data cleanup and validation")
        print("2. Recalculate KPIs with corrected logic")
        print("3. Add missing data relationships")
        print("4. Set target values for KPIs")
        print("5. Implement data validation rules")
    else:
        print("✅ No critical issues found!")
        print("System appears to be functioning correctly.")
    
    print(f"\n📋 DIAGNOSTIC COMPLETE")
    print(f"Issues found: {len(issues)}")
    print(f"Status: {'NEEDS ATTENTION' if issues else 'HEALTHY'}")
