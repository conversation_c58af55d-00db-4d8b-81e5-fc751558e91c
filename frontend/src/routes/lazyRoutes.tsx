/**
 * Lazy-loaded Routes Configuration
 * Code-split routes for better performance
 */

import { createLazyComponent, createRetryableLazyComponent } from '../utils/lazyLoad'

// Public Pages (loaded immediately)
export { default as Home } from '../pages/Home'
export { default as Login } from '../pages/Login'
export { default as HowItWorks } from '../pages/HowItWorks'

// Core Pages (with retry mechanism for critical pages)
export const Dashboard = createRetryableLazyComponent(
  () => import('../pages/Dashboard'),
  3
)

export const Unauthorized = createLazyComponent(
  () => import('../pages/Unauthorized'),
  'جاري تحميل صفحة عدم التصريح...'
)

// Admin Pages
export const UserManagement = createLazyComponent(
  () => import('../pages/admin/UserManagement'),
  'جاري تحميل إدارة المستخدمين...'
)

// Dashboard Pages
export const SuperAdminDashboard = createLazyComponent(
  () => import('../pages/dashboards/SuperAdminDashboard'),
  'جاري تحميل لوحة تحكم المدير العام...'
)

export const AdminDashboard = createLazyComponent(
  () => import('../pages/dashboards/AdminDashboard'),
  'جاري تحميل لوحة تحكم المدير...'
)

// Temporarily disabled due to syntax error
export const HRManagerDashboard = createLazyComponent(
  () => import('../pages/dashboards/AdminDashboard'),
  'جاري تحميل لوحة تحكم مدير الموارد البشرية...'
)

export const FinanceManagerDashboard = createLazyComponent(
  () => import('../pages/dashboards/FinanceManagerDashboard'),
  'جاري تحميل لوحة تحكم مدير المالية...'
)

export const SalesManagerDashboard = createLazyComponent(
  () => import('../pages/dashboards/SalesManagerDashboard'),
  'جاري تحميل لوحة تحكم مدير المبيعات...'
)

export const DepartmentManagerDashboard = createLazyComponent(
  () => import('../pages/dashboards/DepartmentManagerDashboard'),
  'جاري تحميل لوحة تحكم مدير القسم...'
)

export const EmployeeDashboard = createLazyComponent(
  () => import('../pages/dashboards/EmployeeDashboard'),
  'جاري تحميل لوحة تحكم الموظف...'
)

// Core Management Pages
export const Employees = createLazyComponent(
  () => import('../pages/Employees'),
  'جاري تحميل صفحة الموظفين...'
)

export const Departments = createLazyComponent(
  () => import('../pages/Departments'),
  'جاري تحميل صفحة الأقسام...'
)

export const Reports = createLazyComponent(
  () => import('../pages/Reports'),
  'جاري تحميل صفحة التقارير...'
)

export const Settings = createLazyComponent(
  () => import('../pages/Settings'),
  'جاري تحميل صفحة الإعدادات...'
)

// HR Pages
export const Attendance = createLazyComponent(
  () => import('../pages/hr/Attendance'),
  'جاري تحميل صفحة الحضور والانصراف...'
)

export const LeaveManagement = createLazyComponent(
  () => import('../pages/hr/LeaveManagement'),
  'جاري تحميل صفحة إدارة الإجازات...'
)

export const Payroll = createLazyComponent(
  () => import('../pages/hr/Payroll'),
  'جاري تحميل صفحة كشوف المرتبات...'
)

export const Performance = createLazyComponent(
  () => import('../pages/hr/Performance'),
  'جاري تحميل صفحة تقييم الأداء...'
)

// Sales Pages
export const Sales = createLazyComponent(
  () => import('../pages/sales/Sales'),
  'جاري تحميل صفحة المبيعات...'
)

export const SalesOrders = createLazyComponent(
  () => import('../pages/sales/SalesOrders'),
  'جاري تحميل صفحة أوامر المبيعات...'
)

export const Quotations = createLazyComponent(
  () => import('../pages/sales/Quotations'),
  'جاري تحميل صفحة عروض الأسعار...'
)

export const SalesPipeline = createLazyComponent(
  () => import('../pages/sales/SalesPipeline'),
  'جاري تحميل صفحة مسار المبيعات...'
)

// Finance Pages
export const FinanceBudgets = createLazyComponent(
  () => import('../pages/finance-specific/FinanceBudgets'),
  'جاري تحميل صفحة الميزانيات...'
)

export const FinanceReports = createLazyComponent(
  () => import('../pages/finance-specific/FinanceReports'),
  'جاري تحميل صفحة التقارير المالية...'
)

export const Expenses = createLazyComponent(
  () => import('../pages/finance/Expenses'),
  'جاري تحميل صفحة المصروفات...'
)

export const GeneralLedger = createLazyComponent(
  () => import('../pages/finance/GeneralLedger'),
  'جاري تحميل دليل الحسابات...'
)

export const Vendors = createLazyComponent(
  () => import('../pages/finance/Vendors'),
  'جاري تحميل صفحة الموردين...'
)

export const VendorInvoices = createLazyComponent(
  () => import('../pages/finance/VendorInvoices'),
  'جاري تحميل فواتير الموردين...'
)

export const Payments = createLazyComponent(
  () => import('../pages/finance/Payments'),
  'جاري تحميل صفحة المدفوعات...'
)

export const AgingReports = createLazyComponent(
  () => import('../pages/finance/AgingReports'),
  'جاري تحميل تقارير الأعمار...'
)

export const CustomerInvoices = createLazyComponent(
  () => import('../pages/finance/CustomerInvoices'),
  'جاري تحميل فواتير العملاء...'
)

export const Collections = createLazyComponent(
  () => import('../pages/finance/Collections'),
  'جاري تحميل صفحة التحصيلات...'
)

export const FinancialReportsDashboard = createLazyComponent(
  () => import('../pages/finance/FinancialReportsDashboard'),
  'جاري تحميل لوحة التقارير المالية...'
)

export const ProfitLossReport = createLazyComponent(
  () => import('../pages/finance/reports/ProfitLossReport'),
  'جاري تحميل قائمة الدخل...'
)

export const BalanceSheetReport = createLazyComponent(
  () => import('../pages/finance/reports/BalanceSheetReport'),
  'جاري تحميل الميزانية العمومية...'
)

export const CashFlowReport = createLazyComponent(
  () => import('../pages/finance/reports/CashFlowReport'),
  'جاري تحميل قائمة التدفقات النقدية...'
)

export const CurrencyManagement = createLazyComponent(
  () => import('../pages/finance/CurrencyManagement'),
  'جاري تحميل إدارة العملات...'
)

export const ExchangeRateDashboard = createLazyComponent(
  () => import('../pages/finance/ExchangeRateDashboard'),
  'جاري تحميل لوحة أسعار الصرف...'
)

export const AssetDashboard = createLazyComponent(
  () => import('../pages/finance/AssetDashboard'),
  'جاري تحميل لوحة الأصول...'
)

export const AssetManagement = createLazyComponent(
  () => import('../pages/finance/AssetManagement'),
  'جاري تحميل إدارة الأصول...'
)

export const MaintenanceManagement = createLazyComponent(
  () => import('../pages/finance/MaintenanceManagement'),
  'جاري تحميل إدارة الصيانة...'
)

export const ExecutiveDashboard = createLazyComponent(
  () => import('../pages/analytics/ExecutiveDashboard'),
  'جاري تحميل لوحة القيادة التنفيذية...'
)

export const KPIManagement = createLazyComponent(
  () => import('../pages/analytics/KPIManagement'),
  'جاري تحميل إدارة مؤشرات الأداء...'
)

export const ReportsManagement = createLazyComponent(
  () => import('../pages/analytics/ReportsManagement'),
  'جاري تحميل إدارة التقارير...'
)

export const APIManagement = createLazyComponent(
  () => import('../pages/integrations/APIManagement'),
  'جاري تحميل إدارة API...'
)

export const ExternalServices = createLazyComponent(
  () => import('../pages/integrations/ExternalServices'),
  'جاري تحميل الخدمات الخارجية...'
)

export const WebhookManagement = createLazyComponent(
  () => import('../pages/integrations/WebhookManagement'),
  'جاري تحميل إدارة الويب هوك...'
)

export const SecurityDashboard = createLazyComponent(
  () => import('../pages/security/SecurityDashboard'),
  'جاري تحميل لوحة تحكم الأمان...'
)

export const SecurityIncidents = createLazyComponent(
  () => import('../pages/security/SecurityIncidents'),
  'جاري تحميل الحوادث الأمنية...'
)

export const ComplianceManagement = createLazyComponent(
  () => import('../pages/security/ComplianceManagement'),
  'جاري تحميل إدارة الامتثال...'
)

// Project Management Pages
export const Projects = createLazyComponent(
  () => import('../pages/projects/Projects'),
  'جاري تحميل صفحة المشاريع...'
)

export const Tasks = createLazyComponent(
  () => import('../pages/projects/Tasks'),
  'جاري تحميل صفحة المهام...'
)

export const ProjectReports = createLazyComponent(
  () => import('../pages/projects/ProjectReports'),
  'جاري تحميل صفحة تقارير المشاريع...'
)

// Asset Management Pages
export const Assets = createLazyComponent(
  () => import('../pages/assets/Assets'),
  'جاري تحميل صفحة الأصول...'
)

export const Suppliers = createLazyComponent(
  () => import('../pages/assets/Suppliers'),
  'جاري تحميل صفحة الموردين...'
)

export const PurchaseOrders = createLazyComponent(
  () => import('../pages/assets/PurchaseOrders'),
  'جاري تحميل صفحة أوامر الشراء...'
)

// Communication Pages
export const Messages = createLazyComponent(
  () => import('../pages/communication/Messages'),
  'جاري تحميل صفحة الرسائل...'
)

export const Announcements = createLazyComponent(
  () => import('../pages/communication/Announcements'),
  'جاري تحميل صفحة الإعلانات...'
)

export const Documents = createLazyComponent(
  () => import('../pages/communication/Documents'),
  'جاري تحميل صفحة المستندات...'
)

export const Meetings = createLazyComponent(
  () => import('../pages/communication/Meetings'),
  'جاري تحميل صفحة الاجتماعات...'
)

// Personal Pages
export const PersonalProfile = createLazyComponent(
  () => import('../pages/personal/PersonalProfile'),
  'جاري تحميل الملف الشخصي...'
)

export const PersonalMessages = createLazyComponent(
  () => import('../pages/personal/PersonalMessages'),
  'جاري تحميل الرسائل الشخصية...'
)

export const PersonalCalendar = createLazyComponent(
  () => import('../pages/personal/PersonalCalendar'),
  'جاري تحميل التقويم الشخصي...'
)

// Employee Specific Pages
export const EmployeeProfile = createLazyComponent(
  () => import('../pages/employee-specific/EmployeeProfile'),
  'جاري تحميل ملف الموظف...'
)

export const EmployeeLeave = createLazyComponent(
  () => import('../pages/employee-specific/EmployeeLeave'),
  'جاري تحميل طلبات الإجازة...'
)

export const EmployeeTasks = createLazyComponent(
  () => import('../pages/employee-specific/EmployeeTasks'),
  'جاري تحميل مهام الموظف...'
)

// Analytics Pages
export const AnalyticsComponent = createLazyComponent(
  () => import('../components/analytics/AdvancedAnalytics'),
  'جاري تحميل التحليلات المتقدمة...'
)

// Business Intelligence (Now handled by HierarchicalKPIDashboard)
// export const BusinessIntelligence = createLazyComponent(
//   () => import('../pages/analytics/BusinessIntelligence'),
//   'جاري تحميل ذكاء الأعمال...'
// )

// Other Pages
export const Calendar = createLazyComponent(
  () => import('../pages/calendar/Calendar'),
  'جاري تحميل التقويم...'
)

export const Customers = createLazyComponent(
  () => import('../pages/crm/Customers'),
  'جاري تحميل صفحة العملاء...'
)

export const Products = createLazyComponent(
  () => import('../pages/products/Products'),
  'جاري تحميل صفحة المنتجات...'
)

export const Inventory = createLazyComponent(
  () => import('../pages/inventory/Inventory'),
  'جاري تحميل صفحة المخزون...'
)

// Vendor Management
export const VendorManagement = createLazyComponent(
  () => import('../pages/vendors/VendorManagement'),
  'جاري تحميل صفحة إدارة الموردين...'
)

// Advanced Features
export const WorkflowAutomation = createLazyComponent(
  () => import('../pages/workflow/WorkflowManagement'),
  'جاري تحميل أتمتة سير العمل...'
)

export const ReportGenerator = createLazyComponent(
  () => import('../pages/reports/ReportGenerator'),
  'جاري تحميل مولد التقارير...'
)

export const AdvancedDashboard = createLazyComponent(
  () => import('../pages/dashboard/AdvancedDashboard'),
  'جاري تحميل لوحة التحكم المتقدمة...'
)

// Removed TestingDashboard and TestEnhancements - cleaned up test pages

// KPI Pages (Updated to use HierarchicalKPIDashboard)
export const KPIDashboard = createRetryableLazyComponent(
  () => import('../components/kpi/HierarchicalKPIDashboard'),
  3,
  'جاري تحميل لوحة مؤشرات الأداء...'
)



// All KPI dashboards now use HierarchicalKPIDashboard with different configurations
// Removed duplicate dashboard components - use KPIDashboard with dashboardType prop

// Customer Service Pages
export const SupportDashboard = createLazyComponent(
  () => import('../pages/CustomerService/SupportDashboard'),
  'جاري تحميل لوحة خدمة العملاء...'
)

export const TicketManagement = createLazyComponent(
  () => import('../pages/CustomerService/TicketManagement'),
  'جاري تحميل إدارة التذاكر...'
)

export const KnowledgeBase = createLazyComponent(
  () => import('../pages/CustomerService/KnowledgeBase'),
  'جاري تحميل قاعدة المعرفة...'
)

export const LiveChat = createLazyComponent(
  () => import('../pages/CustomerService/LiveChatDashboard'),
  'جاري تحميل لوحة الدردشة المباشرة...'
)

export const LiveChatManagement = createLazyComponent(
  () => import('../pages/CustomerService/LiveChatManagement'),
  'جاري تحميل إدارة الدردشة المباشرة...'
)

export const CustomerFeedback = createLazyComponent(
  () => import('../pages/CustomerService/CustomerFeedback'),
  'جاري تحميل تقييمات العملاء...'
)

export const AgentPerformance = createLazyComponent(
  () => import('../pages/CustomerService/AgentPerformance'),
  'جاري تحميل أداء وكلاء الدعم...'
)

export const AIChatAssistant = createLazyComponent(
  () => import('../pages/CustomerService/AIChatAssistant'),
  'جاري تحميل مساعد الذكاء الاصطناعي...'
)

export const AutomationDashboard = createLazyComponent(
  () => import('../pages/CustomerService/AutomationDashboard'),
  'جاري تحميل لوحة الأتمتة...'
)

export const CustomerSupportHub = createLazyComponent(
  () => import('../pages/CustomerService/CustomerSupportHub'),
  'جاري تحميل مركز دعم العملاء...'
)

// Super Admin Enhanced Features
export const SystemAdministration = createLazyComponent(
  () => import('../pages/admin/SystemAdministration'),
  'جاري تحميل إدارة النظام...'
)

export const SecurityCenter = createLazyComponent(
  () => import('../pages/admin/SecurityCenter'),
  'جاري تحميل مركز الأمان...'
)

export const AIManagement = createLazyComponent(
  () => import('../pages/admin/AIManagement'),
  'جاري تحميل إدارة الذكاء الاصطناعي...'
)

export const AdvancedAnalytics = createLazyComponent(
  () => import('../pages/admin/AdvancedAnalytics'),
  'جاري تحميل التحليلات المتقدمة...'
)

export const ComplianceCenter = createLazyComponent(
  () => import('../pages/admin/ComplianceCenter'),
  'جاري تحميل مركز الامتثال...'
)

export const SuperAdminSystemSettings = createLazyComponent(
  () => import('../pages/admin/SuperAdminSystemSettings'),
  'جاري تحميل إعدادات النظام المتقدمة...'
)

export const SuperAdminSecurityCenter = createLazyComponent(
  () => import('../pages/admin/SuperAdminSecurityCenter'),
  'جاري تحميل مركز الأمان المتقدم...'
)

export const PureSuperAdminDashboard = createLazyComponent(
  () => import('../pages/admin/PureSuperAdminDashboard'),
  'جاري تحميل لوحة تحكم مدير النظام...'
)

// Note: PureSuperAdminRoutes is imported directly in RoleBasedRouter, not as a lazy component

// Route preloading utilities
export const preloadRoutes = {
  dashboard: () => import('../pages/Dashboard'),
  employees: () => import('../pages/Employees'),
  departments: () => import('../pages/Departments'),
  reports: () => import('../pages/Reports'),
  settings: () => import('../pages/Settings'),
}

// Preload critical routes
export const preloadCriticalRoutes = () => {
  // Preload dashboard and core pages
  preloadRoutes.dashboard()
  preloadRoutes.employees()
  preloadRoutes.departments()
}
