"""
Management command to populate KPI data with realistic values
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from ems.models import KPIMetric, KPIMetricValue, Employee


class Command(BaseCommand):
    help = 'Populate KPI metrics with realistic sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days of historical data to generate'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force overwrite existing values'
        )

    def handle(self, *args, **options):
        days = options['days']
        force = options['force']
        
        self.stdout.write(f'🔄 Populating KPI data for the last {days} days...')
        
        # Get all active KPI metrics
        kpi_metrics = KPIMetric.objects.filter(is_active=True)
        
        if not kpi_metrics.exists():
            self.stdout.write(
                self.style.ERROR('❌ No active KPI metrics found. Please create some KPIs first.')
            )
            return
        
        # Get a default employee for calculations
        try:
            default_employee = Employee.objects.first()
            if not default_employee:
                self.stdout.write(
                    self.style.ERROR('❌ No employees found. Please create an employee first.')
                )
                return
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error getting employee: {e}')
            )
            return
        
        total_created = 0
        total_updated = 0
        
        # Generate data for each day
        for day_offset in range(days):
            date = timezone.now().date() - timedelta(days=day_offset)
            
            for kpi_metric in kpi_metrics:
                try:
                    # Check if value already exists
                    existing_value = KPIMetricValue.objects.filter(
                        kpi_metric=kpi_metric,
                        period_start=date,
                        period_end=date
                    ).first()
                    
                    if existing_value and not force:
                        continue
                    
                    # Generate realistic value based on KPI type and target
                    value = self.generate_realistic_value(kpi_metric, day_offset)
                    
                    # Create or update the value
                    kpi_value, created = KPIMetricValue.objects.update_or_create(
                        kpi_metric=kpi_metric,
                        period_start=date,
                        period_end=date,
                        defaults={
                            'value': value,
                            'calculated_by': default_employee,
                            'data_source': 'sample_data_generator',
                            'notes': f'Generated sample data for {date}',
                            'is_manual': False
                        }
                    )
                    
                    if created:
                        total_created += 1
                    else:
                        total_updated += 1
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️ Error creating value for {kpi_metric.name}: {e}')
                    )
                    continue
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ KPI data population completed!\n'
                f'   📊 Created: {total_created} new values\n'
                f'   🔄 Updated: {total_updated} existing values\n'
                f'   📈 Total KPIs: {kpi_metrics.count()}\n'
                f'   📅 Days covered: {days}'
            )
        )

    def generate_realistic_value(self, kpi_metric, day_offset):
        """Generate realistic KPI values based on the metric type and target"""
        
        target = float(kpi_metric.target_value) if kpi_metric.target_value else 100.0
        
        # Add some randomness and trend
        base_variance = 0.15  # 15% variance
        trend_factor = 1.0 - (day_offset * 0.01)  # Slight improvement over time
        
        # Different patterns based on metric type
        if kpi_metric.metric_type == 'FINANCIAL':
            # Financial metrics tend to have more volatility
            variance = random.uniform(-base_variance * 1.5, base_variance * 1.5)
            value = target * (1 + variance) * trend_factor
            
        elif kpi_metric.metric_type == 'HR':
            # HR metrics are usually more stable
            variance = random.uniform(-base_variance * 0.8, base_variance * 0.8)
            value = target * (1 + variance) * trend_factor
            
        elif kpi_metric.metric_type == 'OPERATIONAL':
            # Operational metrics can vary more
            variance = random.uniform(-base_variance * 1.2, base_variance * 1.2)
            value = target * (1 + variance) * trend_factor
            
        else:
            # Default pattern
            variance = random.uniform(-base_variance, base_variance)
            value = target * (1 + variance) * trend_factor
        
        # Ensure value is not negative for most metrics
        if kpi_metric.name.lower() not in ['turnover', 'cost', 'expense']:
            value = max(0, value)
        
        # Round to appropriate decimal places
        decimal_places = kpi_metric.decimal_places or 2
        return round(Decimal(str(value)), decimal_places)
