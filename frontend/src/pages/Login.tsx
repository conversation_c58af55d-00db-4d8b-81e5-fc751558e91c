import { useState, useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Navigate, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { AppDispatch, RootState } from '../store'
import { loginUser, clearError } from '../store/slices/authSlice'
import ForgotPassword from '../components/ForgotPassword'
import { useCSRF } from '../hooks/useCSRF'
import { checkLoginRateLimit, logSecurityEvent } from '../utils/securityHeaders'
import { announce, accessibility } from '../utils/accessibility'
import { KEYBOARD_KEYS } from '../utils/accessibilityEnhancements'

import {
  Eye,
  EyeOff,
  Lock,
  User
} from 'lucide-react'

interface LoginProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    login: 'تسجيل الدخول',
    welcome: 'مرحباً بك في نمو',
    subtitle: 'نظام إدارة المؤسسات المتكامل',
    email: 'اسم المستخدم',
    password: 'كلمة المرور',
    loginButton: 'تسجيل الدخول',
    forgotPassword: 'نسيت كلمة المرور؟',
    rememberMe: 'تذكرني',

    loginAs: 'تسجيل الدخول كـ',
    emailPlaceholder: 'أدخل اسم المستخدم',
    passwordPlaceholder: 'أدخل كلمة المرور',
    loggingIn: 'جاري تسجيل الدخول...',
    invalidCredentials: 'بيانات الدخول غير صحيحة',
    loginFailed: 'فشل في تسجيل الدخول',
    tooManyAttempts: 'محاولات كثيرة، حاول مرة أخرى لاحقاً'
  },
  en: {
    login: 'Login',
    welcome: 'Welcome to Numu',
    subtitle: 'Integrated Enterprise Management System',
    email: 'Username',
    password: 'Password',
    loginButton: 'Login',
    forgotPassword: 'Forgot Password?',
    rememberMe: 'Remember Me',

    loginAs: 'Login as',
    emailPlaceholder: 'Enter your username',
    passwordPlaceholder: 'Enter your password',
    loggingIn: 'Logging in...',
    invalidCredentials: 'Invalid credentials',
    loginFailed: 'Login failed',
    tooManyAttempts: 'Too many attempts, try again later'
  }
}

// Mock demo accounts removed - Authentication should use real backend accounts
export default function Login({ language }: LoginProps) {
  const dispatch = useDispatch<AppDispatch>()
  const navigate = useNavigate()
  const { isAuthenticated, isLoading, error, loginAttempts } = useSelector((state: RootState) => state.auth)
  const { ensureToken } = useCSRF()

  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{
    username?: string
    password?: string
  }>({})
  const [showForgotPassword, setShowForgotPassword] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Enhanced accessibility: Apply RTL styles and announce page load
  useEffect(() => {
    // Apply RTL styles
    const direction = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.dir = direction
    document.documentElement.lang = language

    // Add RTL class to body for CSS targeting
    if (direction === 'rtl') {
      document.body.classList.add('rtl')
      document.body.classList.remove('ltr')
    } else {
      document.body.classList.add('ltr')
      document.body.classList.remove('rtl')
    }

    announce(
      language === 'ar' ? 'صفحة تسجيل الدخول محملة' : 'Login page loaded'
    )
  }, [language])

  // Helper function to get user-friendly error messages
  const getErrorMessage = (error: string): string => {
    if (loginAttempts >= 5) {
      return language === 'ar' ? 'تم تجاوز الحد الأقصى للمحاولات' : 'Maximum attempts exceeded'
    }
    if (error.includes('throttled') || error.includes('Request throttled')) {
      return language === 'ar' ? 'تم تجاوز عدد المحاولات المسموحة' : 'Too many login attempts'
    }
    if (error.includes('Username not found')) {
      return language === 'ar' ? 'اسم المستخدم غير موجود' : 'Username not found'
    }
    if (error.includes('Invalid password')) {
      return language === 'ar' ? 'كلمة المرور غير صحيحة' : 'Invalid password'
    }
    if (error.includes('account has been deactivated') || error.includes('ACCOUNT_DEACTIVATED')) {
      return language === 'ar' ? 'تم إلغاء تفعيل الحساب' : 'Account deactivated'
    }
    if (error.includes('Authentication failed') || error.includes('Invalid credentials')) {
      return language === 'ar' ? 'بيانات الدخول غير صحيحة' : 'Invalid username or password'
    }
    if (error.includes('Network error')) {
      return language === 'ar' ? 'خطأ في الاتصال' : 'Connection error'
    }
    if (error.includes('must be at least')) {
      return language === 'ar' ? 'بيانات غير صحيحة' : 'Invalid input'
    }
    return language === 'ar' ? 'خطأ في تسجيل الدخول' : 'Login error'
  }

  // Helper function to get error help text
  const getErrorHelpText = (error: string): string => {
    if (loginAttempts >= 5) {
      return language === 'ar' ? 'يرجى المحاولة مرة أخرى بعد 5 دقائق' : 'Please try again after 5 minutes'
    }
    if (error.includes('throttled') || error.includes('Request throttled')) {
      return language === 'ar' ? 'يرجى الانتظار قليلاً ثم المحاولة مرة أخرى' : 'Please wait a moment and try again'
    }
    if (error.includes('Username not found')) {
      return language === 'ar' ? 'تأكد من كتابة اسم المستخدم بشكل صحيح' : 'Please check your username spelling'
    }
    if (error.includes('Invalid password')) {
      return language === 'ar' ? 'تأكد من كتابة كلمة المرور بشكل صحيح' : 'Please check your password'
    }
    if (error.includes('account has been deactivated') || error.includes('ACCOUNT_DEACTIVATED')) {
      return language === 'ar' ? 'تواصل مع الدعم الفني لإعادة تفعيل الحساب' : 'Contact support to reactivate your account'
    }
    if (error.includes('Network error')) {
      return language === 'ar' ? 'تحقق من اتصالك بالإنترنت' : 'Please check your internet connection'
    }
    return language === 'ar' ? 'يرجى المحاولة مرة أخرى' : 'Please try again'
  }

  // Validation functions
  const validateUsername = (username: string): string | null => {
    if (!username.trim()) {
      return language === 'ar' ? 'اسم المستخدم مطلوب' : 'Username is required'
    }
    if (username.length < 3) {
      return language === 'ar' ? 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' : 'Username must be at least 3 characters'
    }
    return null
  }

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return language === 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required'
    }
    if (password.length < 4) {
      return language === 'ar' ? 'كلمة المرور يجب أن تكون 4 أحرف على الأقل' : 'Password must be at least 4 characters'
    }
    return null
  }

  // Input handlers with validation
  const handleUsernameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, username: value }))

    // Clear validation error when user starts typing
    if (validationErrors.username) {
      setValidationErrors(prev => ({ ...prev, username: undefined }))
    }
    // Clear general error when user makes changes
    if (error) {
      dispatch(clearError())
    }
  }, [validationErrors.username, error, dispatch])

  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, password: value }))

    // Clear validation error when user starts typing
    if (validationErrors.password) {
      setValidationErrors(prev => ({ ...prev, password: undefined }))
    }
    // Clear general error when user makes changes
    if (error) {
      dispatch(clearError())
    }
  }, [validationErrors.password, error, dispatch])

  const handleRememberMeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setRememberMe(e.target.checked)
  }, [])

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === KEYBOARD_KEYS.ESCAPE) {
      // Clear form on escape
      setFormData({ username: '', password: '' })
      setValidationErrors({})
      dispatch(clearError())
      announce(
        language === 'ar' ? 'تم مسح النموذج' : 'Form cleared'
      )
    }
  }, [language, dispatch])

  // Don't clear error on mount - let user see previous login errors

  // Development debugging utilities - FIXED: Removed dependencies to prevent infinite re-renders
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      ;(window as any).debugLogin = {
        testLogin: async (username: string, password: string) => {
          try {
            const result = await dispatch(loginUser({ username, password })).unwrap()
            return result
          } catch (error) {
            throw error
          }
        },
        getCurrentState: () => ({ formData, isLoading, loginAttempts, error, isAuthenticated }),
        clearErrors: () => {
          dispatch(clearError())
          setValidationErrors({})
        }
      }
    }
  }, []) // FIXED: Empty dependency array to run only once

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    // Prevent default form submission and any page reload
    e.preventDefault()
    e.stopPropagation()

    // Additional safety to prevent form submission
    if (e.target) {
      (e.target as HTMLFormElement).onsubmit = () => false
    }

    try {
      // Prevent any navigation during login
      window.onbeforeunload = () => "Login in progress..."

      if (loginAttempts >= 5) {
        window.onbeforeunload = null
        return
      }

      if (isLoading) {
        return
      }

      // Clear any previous errors
      dispatch(clearError())
      setValidationErrors({})

      // SECURITY FIX: Check rate limiting before attempting login
      const rateLimitResult = checkLoginRateLimit(formData.username)
      if (!rateLimitResult.allowed) {
        const resetTime = new Date(rateLimitResult.resetTime)
        const errorMessage = language === 'ar'
          ? `تم تجاوز الحد الأقصى من محاولات تسجيل الدخول. يرجى المحاولة مرة أخرى في ${resetTime.toLocaleTimeString()}`
          : `Too many login attempts. Please try again at ${resetTime.toLocaleTimeString()}`

        logSecurityEvent('login_rate_limit_exceeded', {
          username: formData.username,
          resetTime: rateLimitResult.resetTime
        })

        setValidationErrors({ username: errorMessage })
        window.onbeforeunload = null
        return
      }

      // SECURITY FIX: Ensure CSRF token is available before login
      await ensureToken()

      // Validate form data
      const usernameError = validateUsername(formData.username)
      const passwordError = validatePassword(formData.password)

      if (usernameError || passwordError) {
        setValidationErrors({
          username: usernameError || undefined,
          password: passwordError || undefined
        })
        window.onbeforeunload = null
        return
      }

      await dispatch(loginUser(formData)).unwrap()

      // Clear the beforeunload handler
      window.onbeforeunload = null

      // Announce successful login
      announce(
        language === 'ar' ? 'تم تسجيل الدخول بنجاح' : 'Login successful',
        'assertive'
      )

      // Navigate to dashboard
      setTimeout(() => {
        navigate('/', { replace: true })
      }, 100)

    } catch (error: any) {
      window.onbeforeunload = null

      // Announce login error to screen readers
      announce(
        language === 'ar' ? 'فشل تسجيل الدخول' : 'Login failed',
        'assertive'
      )

      // Handle backend field validation errors
      if (error?.details?.field_errors) {
        const fieldErrors: any = {}
        const backendErrors = error.details.field_errors

        if (backendErrors.username) {
          fieldErrors.username = Array.isArray(backendErrors.username)
            ? backendErrors.username[0]
            : backendErrors.username
        }
        if (backendErrors.password) {
          fieldErrors.password = Array.isArray(backendErrors.password)
            ? backendErrors.password[0]
            : backendErrors.password
        }

        if (Object.keys(fieldErrors).length > 0) {
          setValidationErrors(fieldErrors)
        }
      }

    }
  }



  return (
    <main className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 login-container ${isRTL ? 'rtl' : 'ltr'}`} role="main" aria-label={language === 'ar' ? 'صفحة تسجيل الدخول' : 'Login page'}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden" aria-hidden="true">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Branding */}
        <header className="flex flex-col justify-center space-y-8" role="banner">
          <section className="text-center lg:text-right" aria-labelledby="welcome-heading">
            <h1 id="welcome-heading" className="text-5xl font-bold text-white mb-4 drop-shadow-lg animate-fade-in-up">
              {t.welcome}
            </h1>
            <p className="text-xl text-white/80 mb-8 animate-fade-in-up animation-delay-200">
              {t.subtitle}
            </p>
          </section>
        </header>

        {/* Right Side - Login Form */}
        <section className="flex flex-col justify-center animate-fade-in-up animation-delay-400" aria-labelledby="login-heading" role="form">
          {showForgotPassword ? (
            <ForgotPassword
              language={language}
              onBack={() => setShowForgotPassword(false)}
            />
          ) : (
            <Card className="glass-card border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300">
            <CardHeader className="text-center">
              <CardTitle id="login-heading" className="text-2xl font-bold text-white">{t.login}</CardTitle>
              <CardDescription className="text-white/70">
                {language === 'ar' ? 'أدخل بياناتك للوصول إلى النظام' : 'Enter your credentials to access the system'}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">

              {/* Error Message */}
              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 relative" role="alert" aria-live="polite" aria-atomic="true">
                  <button
                    type="button"
                    onClick={() => dispatch(clearError())}
                    className="absolute top-2 right-2 text-red-400 hover:text-red-300 transition-colors text-lg"
                    aria-label={language === 'ar' ? 'إغلاق رسالة الخطأ' : 'Dismiss error message'}
                  >
                    ×
                  </button>
                  <div className="pr-6">
                    <p className="text-red-400 text-sm font-medium mb-1">
                      {getErrorMessage(error)}
                    </p>
                    <p className="text-red-300 text-xs mb-2">
                      {getErrorHelpText(error)}
                    </p>

                    {/* Retry button for throttled requests */}
                    {(error.includes('throttled') || error.includes('Request throttled')) && (
                      <button
                        type="button"
                        onClick={() => {
                          dispatch(clearError())
                          setValidationErrors({})
                          // Clear throttling in development
                          if (process.env.NODE_ENV === 'development' && (window as any).clearLoginThrottling) {
                            (window as any).clearLoginThrottling()
                          }
                        }}
                        className="text-xs bg-red-500/30 hover:bg-red-500/50 text-red-200 px-3 py-1 rounded transition-colors"
                      >
                        {language === 'ar' ? 'إعادة المحاولة الآن' : 'Retry Now'}
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Login Form */}
              <form
                onSubmit={(e) => {
                  console.log('📝 Form onSubmit triggered')
                  handleSubmit(e)
                  return false
                }}
                onKeyDown={handleKeyDown}
                className="space-y-4"
                noValidate
                autoComplete="off"
                method="POST"
                aria-labelledby="login-heading"
                role="form"
              >
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-white">{t.email}</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                    <Input
                      id="username"
                      name="username"
                      type="text"
                      placeholder={t.emailPlaceholder}
                      value={formData.username}
                      onChange={handleUsernameChange}
                      className={`pl-10 glass-input ${validationErrors.username ? 'border-red-500 focus:border-red-500' : ''}`}
                      required
                      disabled={isLoading || loginAttempts >= 5}
                      autoComplete="username"
                    />
                  </div>
                  {validationErrors.username && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.username}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-white">{t.password}</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder={t.passwordPlaceholder}
                      value={formData.password}
                      onChange={handlePasswordChange}
                      className={`pl-10 pr-10 glass-input ${validationErrors.password ? 'border-red-500 focus:border-red-500' : ''}`}
                      required
                      disabled={isLoading || loginAttempts >= 5}
                      autoComplete="current-password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {validationErrors.password && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.password}</p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="remember"
                      name="remember"
                      checked={rememberMe}
                      onChange={handleRememberMeChange}
                      className="rounded border-white/30 bg-white/10"
                    />
                    <Label htmlFor="remember" className="text-white/80 text-sm">
                      {t.rememberMe}
                    </Label>
                  </div>
                  <Button
                    variant="link"
                    className="text-blue-400 hover:text-blue-300 p-0"
                    onClick={() => setShowForgotPassword(true)}
                    type="button"
                  >
                    {t.forgotPassword}
                  </Button>
                </div>

                <Button
                  type="submit"

                  className={`w-full glass-button font-semibold py-3 transition-all duration-300 ${
                    (error && (error.includes('throttled') || error.includes('Request throttled')))
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600'
                      : 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600'
                  } text-white`}
                  disabled={isLoading || loginAttempts >= 5 || !formData.username || !formData.password}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t.loggingIn}
                    </div>
                  ) : loginAttempts >= 5 ? (
                    language === 'ar' ? 'تم تجاوز الحد الأقصى' : 'Too Many Attempts'
                  ) : (error && (error.includes('throttled') || error.includes('Request throttled'))) ? (
                    language === 'ar' ? 'محاولة مرة أخرى' : 'Try Again'
                  ) : (
                    t.loginButton
                  )}
                </Button>


              </form>

              {/* Status indicator */}
              <div className="text-center text-xs text-white/50 mt-4">
                {loginAttempts > 0 && (
                  <p>
                    {language === 'ar'
                      ? `المحاولات: ${loginAttempts}/5`
                      : `Attempts: ${loginAttempts}/5`}
                  </p>
                )}
                {(error && (error.includes('throttled') || error.includes('Request throttled'))) && (
                  <p className="text-orange-400 mt-1">
                    {language === 'ar'
                      ? 'تم تقييد الطلبات مؤقتاً'
                      : 'Requests temporarily limited'}
                  </p>
                )}
              </div>

            </CardContent>
          </Card>
          )}
        </section>
      </div>
    </main>
  )
}
