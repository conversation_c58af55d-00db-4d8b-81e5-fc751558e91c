"""
Management command to create real business data foundation
This creates actual business entities that will drive real KPI calculations
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import random

from ems.models import (
    Employee, Department, Customer, Vendor,
    Project, Task, Expense, CustomerInvoice, VendorInvoice,
    Attendance, LeaveRequest, LeaveType
)


class Command(BaseCommand):
    help = 'Setup real business data foundation for automatic KPI calculations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-name',
            type=str,
            default='TechCorp Solutions',
            help='Company name for business setup'
        )
        parser.add_argument(
            '--employees',
            type=int,
            default=25,
            help='Number of employees to create'
        )
        parser.add_argument(
            '--projects',
            type=int,
            default=8,
            help='Number of projects to create'
        )

    def handle(self, *args, **options):
        self.company_name = options['company_name']
        self.employee_count = options['employees']
        self.project_count = options['projects']
        
        self.stdout.write(
            self.style.SUCCESS(f'🏢 Setting up real business data for {self.company_name}...')
        )
        
        # Step 1: Create real departments
        departments = self.create_real_departments()
        
        # Step 2: Create real employees
        employees = self.create_real_employees(departments)
        
        # Step 4: Create real customers
        customers = self.create_real_customers()
        
        # Step 5: Create real vendors
        vendors = self.create_real_vendors()
        
        # Step 6: Create leave types
        leave_types = self.create_leave_types()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Real business foundation created!\n'
                f'   🏢 Company: {self.company_name}\n'
                f'   🏬 Departments: {len(departments)}\n'
                f'   👥 Employees: {len(employees)}\n'
                f'   🤝 Customers: {len(customers)}\n'
                f'   🏭 Vendors: {len(vendors)}\n'
                f'   🏖️ Leave Types: {len(leave_types)}'
            )
        )

    def create_real_departments(self):
        """Create actual business departments"""
        departments_data = [
            {
                'name': 'Executive Management',
                'name_ar': 'الإدارة التنفيذية',
                'description': 'Senior leadership and strategic direction',
                'description_ar': 'القيادة العليا والتوجه الاستراتيجي',
                'budget_amount': Decimal('500000.00'),
                'is_active': True
            },
            {
                'name': 'Human Resources',
                'name_ar': 'الموارد البشرية',
                'description': 'Employee management and organizational development',
                'description_ar': 'إدارة الموظفين والتطوير التنظيمي',
                'budget_amount': Decimal('200000.00'),
                'is_active': True
            },
            {
                'name': 'Information Technology',
                'name_ar': 'تقنية المعلومات',
                'description': 'Technology infrastructure and software development',
                'description_ar': 'البنية التحتية التقنية وتطوير البرمجيات',
                'budget_amount': Decimal('800000.00'),
                'is_active': True
            },
            {
                'name': 'Finance & Accounting',
                'name_ar': 'المالية والمحاسبة',
                'description': 'Financial management and accounting operations',
                'description_ar': 'الإدارة المالية والعمليات المحاسبية',
                'budget_amount': Decimal('300000.00'),
                'is_active': True
            },
            {
                'name': 'Sales & Marketing',
                'name_ar': 'المبيعات والتسويق',
                'description': 'Revenue generation and market development',
                'description_ar': 'توليد الإيرادات وتطوير السوق',
                'budget_amount': Decimal('600000.00'),
                'is_active': True
            },
            {
                'name': 'Operations',
                'name_ar': 'العمليات',
                'description': 'Daily operations and service delivery',
                'description_ar': 'العمليات اليومية وتقديم الخدمات',
                'budget_amount': Decimal('400000.00'),
                'is_active': True
            },
            {
                'name': 'Customer Success',
                'name_ar': 'نجاح العملاء',
                'description': 'Customer support and relationship management',
                'description_ar': 'دعم العملاء وإدارة العلاقات',
                'budget_amount': Decimal('250000.00'),
                'is_active': True
            }
        ]
        
        departments = []
        for dept_data in departments_data:
            department, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults=dept_data
            )
            departments.append(department)
            if created:
                self.stdout.write(f'  ✅ Created department: {department.name}')
        
        return departments

    def create_real_employees(self, departments):
        """Create actual employee records with realistic data"""
        # Real employee data with department mapping
        employees_data = [
            # Executive team
            {'first_name': 'Ahmed', 'last_name': 'Al-Rashid', 'email': '<EMAIL>', 'position': 'Chief Executive Officer', 'position_ar': 'الرئيس التنفيذي', 'department': 'Executive Management', 'salary': 25000, 'hire_date_days_ago': 1200},
            {'first_name': 'Sarah', 'last_name': 'Johnson', 'email': '<EMAIL>', 'position': 'Chief Technology Officer', 'position_ar': 'مدير التقنية', 'department': 'Information Technology', 'salary': 22000, 'hire_date_days_ago': 800},
            {'first_name': 'Omar', 'last_name': 'Al-Mahmoud', 'email': '<EMAIL>', 'position': 'Chief Financial Officer', 'position_ar': 'المدير المالي', 'department': 'Finance & Accounting', 'salary': 20000, 'hire_date_days_ago': 900},

            # Management team
            {'first_name': 'Fatima', 'last_name': 'Al-Zahra', 'email': '<EMAIL>', 'position': 'HR Manager', 'position_ar': 'مدير الموارد البشرية', 'department': 'Human Resources', 'salary': 15000, 'hire_date_days_ago': 600},
            {'first_name': 'Mohammed', 'last_name': 'Al-Saud', 'email': '<EMAIL>', 'position': 'Sales Manager', 'position_ar': 'مدير المبيعات', 'department': 'Sales & Marketing', 'salary': 16000, 'hire_date_days_ago': 700},
            {'first_name': 'Layla', 'last_name': 'Al-Faisal', 'email': '<EMAIL>', 'position': 'Operations Manager', 'position_ar': 'مدير العمليات', 'department': 'Operations', 'salary': 14000, 'hire_date_days_ago': 500},

            # Senior staff
            {'first_name': 'Khalid', 'last_name': 'Al-Otaibi', 'email': '<EMAIL>', 'position': 'Senior Software Engineer', 'position_ar': 'مهندس برمجيات أول', 'department': 'Information Technology', 'salary': 12000, 'hire_date_days_ago': 400},
            {'first_name': 'Nora', 'last_name': 'Al-Mansouri', 'email': '<EMAIL>', 'position': 'Senior Accountant', 'position_ar': 'محاسب أول', 'department': 'Finance & Accounting', 'salary': 11000, 'hire_date_days_ago': 450},
            {'first_name': 'Yusuf', 'last_name': 'Al-Harbi', 'email': '<EMAIL>', 'position': 'Senior Sales Representative', 'position_ar': 'مندوب مبيعات أول', 'department': 'Sales & Marketing', 'salary': 10000, 'hire_date_days_ago': 350},

            # Regular staff
            {'first_name': 'Aisha', 'last_name': 'Al-Qasimi', 'email': '<EMAIL>', 'position': 'Software Developer', 'position_ar': 'مطور برمجيات', 'department': 'Information Technology', 'salary': 8000, 'hire_date_days_ago': 200},
            {'first_name': 'Hassan', 'last_name': 'Al-Dosari', 'email': '<EMAIL>', 'position': 'Software Developer', 'position_ar': 'مطور برمجيات', 'department': 'Information Technology', 'salary': 7500, 'hire_date_days_ago': 180},
            {'first_name': 'Maryam', 'last_name': 'Al-Thani', 'email': '<EMAIL>', 'position': 'Accountant', 'position_ar': 'محاسب', 'department': 'Finance & Accounting', 'salary': 7000, 'hire_date_days_ago': 250},
            {'first_name': 'Abdullah', 'last_name': 'Al-Ghamdi', 'email': '<EMAIL>', 'position': 'Sales Representative', 'position_ar': 'مندوب مبيعات', 'department': 'Sales & Marketing', 'salary': 6500, 'hire_date_days_ago': 150},
            {'first_name': 'Zainab', 'last_name': 'Al-Kuwari', 'email': '<EMAIL>', 'position': 'HR Specialist', 'position_ar': 'أخصائي موارد بشرية', 'department': 'Human Resources', 'salary': 6000, 'hire_date_days_ago': 120},
            {'first_name': 'Ali', 'last_name': 'Al-Shamsi', 'email': '<EMAIL>', 'position': 'Customer Support Specialist', 'position_ar': 'أخصائي دعم العملاء', 'department': 'Customer Success', 'salary': 5500, 'hire_date_days_ago': 100},
        ]

        dept_map = {dept.name: dept for dept in departments}
        employees = []

        for emp_data in employees_data:
            department = dept_map.get(emp_data['department'])
            if not department:
                continue

            # Create user account
            username = f"{emp_data['first_name'].lower()}.{emp_data['last_name'].lower().replace('-', '')}"
            user, user_created = User.objects.get_or_create(
                username=username,
                defaults={
                    'first_name': emp_data['first_name'],
                    'last_name': emp_data['last_name'],
                    'email': emp_data['email'],
                    'is_active': True
                }
            )

            if user_created:
                user.set_password('password123')  # Default password
                user.save()

            # Create employee record
            hire_date = timezone.now().date() - timedelta(days=emp_data['hire_date_days_ago'])
            employee_id = f"EMP{len(employees) + 1:04d}"

            employee, emp_created = Employee.objects.get_or_create(
                user=user,
                defaults={
                    'employee_id': employee_id,
                    'position': emp_data['position'],
                    'position_ar': emp_data['position_ar'],
                    'department': department,
                    'hire_date': hire_date,
                    'salary': Decimal(str(emp_data['salary'])),
                    'phone': f"+966501234{len(employees):03d}",
                    'emergency_contact': f"Emergency Contact {len(employees) + 1}",
                    'emergency_phone': f"+966502345{len(employees):03d}",
                    'is_active': True,
                    'employment_status': 'FULL_TIME',
                    'gender': 'M'  # Default gender
                }
            )

            employees.append(employee)
            if emp_created:
                self.stdout.write(f'  ✅ Created employee: {employee.user.get_full_name()} ({employee.position})')

        return employees

    def create_real_customers(self):
        """Create actual customer records"""
        customers_data = [
            {'company_name': 'Saudi Aramco', 'contact_name': 'Ahmed Al-Rashid', 'email': '<EMAIL>', 'phone': '+*********001'},
            {'company_name': 'SABIC', 'contact_name': 'Fatima Al-Zahra', 'email': '<EMAIL>', 'phone': '+*********002'},
            {'company_name': 'STC (Saudi Telecom)', 'contact_name': 'Mohammed Al-Saud', 'email': '<EMAIL>', 'phone': '+*********003'},
            {'company_name': 'Al Rajhi Bank', 'contact_name': 'Layla Al-Faisal', 'email': '<EMAIL>', 'phone': '+*********004'},
            {'company_name': 'SAMBA Financial Group', 'contact_name': 'Omar Al-Mahmoud', 'email': '<EMAIL>', 'phone': '+*********005'},
            {'company_name': 'Riyadh Municipality', 'contact_name': 'Nora Al-Mansouri', 'email': '<EMAIL>', 'phone': '+*********006'},
            {'company_name': 'King Saud University', 'contact_name': 'Khalid Al-Otaibi', 'email': '<EMAIL>', 'phone': '+*********007'},
            {'company_name': 'Almarai Company', 'contact_name': 'Aisha Al-Qasimi', 'email': '<EMAIL>', 'phone': '+*********008'},
        ]

        customers = []
        for cust_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                company_name=cust_data['company_name'],
                defaults={
                    'first_name': cust_data['contact_name'].split()[0],
                    'last_name': ' '.join(cust_data['contact_name'].split()[1:]),
                    'email': cust_data['email'],
                    'phone': cust_data['phone'],
                    'address_line1': f"Office Address for {cust_data['company_name']}",
                    'city': 'Riyadh',
                    'country': 'Saudi Arabia',
                    'customer_type': 'BUSINESS',
                    'status': 'ACTIVE'
                }
            )
            customers.append(customer)
            if created:
                self.stdout.write(f'  ✅ Created customer: {customer.company_name}')

        return customers

    def create_real_vendors(self):
        """Create actual vendor records"""
        vendors_data = [
            {'company_name': 'Microsoft Saudi Arabia', 'contact_name': 'Sarah Johnson', 'email': '<EMAIL>'},
            {'company_name': 'Oracle Middle East', 'contact_name': 'Hassan Al-Dosari', 'email': '<EMAIL>'},
            {'company_name': 'IBM Saudi Arabia', 'contact_name': 'Maryam Al-Thani', 'email': '<EMAIL>'},
            {'company_name': 'Cisco Systems KSA', 'contact_name': 'Abdullah Al-Ghamdi', 'email': '<EMAIL>'},
            {'company_name': 'Dell Technologies', 'contact_name': 'Zainab Al-Kuwari', 'email': '<EMAIL>'},
            {'company_name': 'HP Enterprise KSA', 'contact_name': 'Ali Al-Shamsi', 'email': '<EMAIL>'},
        ]

        vendors = []
        for vend_data in vendors_data:
            vendor_code = f"VND{len(vendors) + 1:04d}"
            vendor, created = Vendor.objects.get_or_create(
                company_name=vend_data['company_name'],
                defaults={
                    'vendor_code': vendor_code,
                    'contact_person': vend_data['contact_name'],
                    'email': vend_data['email'],
                    'phone': f"+*********{len(vendors) + 100:03d}",
                    'address': f"Vendor Address for {vend_data['company_name']}, Riyadh, Saudi Arabia",
                    'is_active': True,
                    'is_approved': True
                }
            )
            vendors.append(vendor)
            if created:
                self.stdout.write(f'  ✅ Created vendor: {vendor.company_name}')

        return vendors

    def create_leave_types(self):
        """Create standard leave types"""
        leave_types_data = [
            {'name': 'Annual Leave', 'name_ar': 'إجازة سنوية', 'days_allowed': 30, 'is_paid': True},
            {'name': 'Sick Leave', 'name_ar': 'إجازة مرضية', 'days_allowed': 15, 'is_paid': True},
            {'name': 'Emergency Leave', 'name_ar': 'إجازة طارئة', 'days_allowed': 5, 'is_paid': True},
            {'name': 'Maternity Leave', 'name_ar': 'إجازة أمومة', 'days_allowed': 70, 'is_paid': True},
            {'name': 'Paternity Leave', 'name_ar': 'إجازة أبوة', 'days_allowed': 3, 'is_paid': True},
            {'name': 'Unpaid Leave', 'name_ar': 'إجازة بدون راتب', 'days_allowed': 30, 'is_paid': False},
        ]

        leave_types = []
        for lt_data in leave_types_data:
            leave_type, created = LeaveType.objects.get_or_create(
                name=lt_data['name'],
                defaults=lt_data
            )
            leave_types.append(leave_type)
            if created:
                self.stdout.write(f'  ✅ Created leave type: {leave_type.name}')

        return leave_types
