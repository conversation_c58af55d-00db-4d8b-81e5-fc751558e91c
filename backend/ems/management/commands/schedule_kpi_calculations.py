"""
Management command to set up automated KPI calculation scheduling
This creates a robust scheduling system for regular KPI updates
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.management import call_command
from datetime import datetime, timedelta
import schedule
import time
import threading
import logging

from ems.models import KPIMetric, KPIMetricValue, Employee

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up automated KPI calculation scheduling'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mode',
            type=str,
            choices=['setup', 'run', 'status'],
            default='setup',
            help='Mode: setup schedules, run scheduler, or show status'
        )
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='Run scheduler as daemon (background process)'
        )

    def handle(self, *args, **options):
        mode = options['mode']
        
        if mode == 'setup':
            self.setup_schedules()
        elif mode == 'run':
            self.run_scheduler(daemon=options['daemon'])
        elif mode == 'status':
            self.show_status()

    def setup_schedules(self):
        """Set up KPI calculation schedules"""
        self.stdout.write(
            self.style.SUCCESS('🕒 Setting up KPI calculation schedules...')
        )
        
        # Daily KPI calculations (every day at 6 AM)
        schedule.every().day.at("06:00").do(self.calculate_daily_kpis)
        
        # Weekly KPI calculations (every Monday at 7 AM)
        schedule.every().monday.at("07:00").do(self.calculate_weekly_kpis)
        
        # Monthly KPI calculations (1st of every month at 8 AM)
        schedule.every().month.do(self.calculate_monthly_kpis)
        
        # Real-time KPI calculations (every 4 hours)
        schedule.every(4).hours.do(self.calculate_realtime_kpis)
        
        self.stdout.write(
            self.style.SUCCESS(
                '✅ Schedules configured:\n'
                '   📅 Daily KPIs: Every day at 6:00 AM\n'
                '   📅 Weekly KPIs: Every Monday at 7:00 AM\n'
                '   📅 Monthly KPIs: 1st of month at 8:00 AM\n'
                '   📅 Real-time KPIs: Every 4 hours'
            )
        )

    def run_scheduler(self, daemon=False):
        """Run the KPI calculation scheduler"""
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting KPI calculation scheduler...')
        )
        
        if daemon:
            # Run as daemon in background
            scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            scheduler_thread.start()
            self.stdout.write(
                self.style.SUCCESS('✅ Scheduler running as daemon process')
            )
        else:
            # Run in foreground
            self._scheduler_loop()

    def _scheduler_loop(self):
        """Main scheduler loop"""
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except KeyboardInterrupt:
                logger.info("Scheduler stopped by user")
                break
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(300)  # Wait 5 minutes before retrying

    def calculate_daily_kpis(self):
        """Calculate daily KPIs"""
        logger.info("🔄 Running daily KPI calculations...")
        try:
            call_command('calculate_real_kpis', period='daily', months=1)
            logger.info("✅ Daily KPI calculations completed")
        except Exception as e:
            logger.error(f"❌ Daily KPI calculation failed: {e}")

    def calculate_weekly_kpis(self):
        """Calculate weekly KPIs"""
        logger.info("🔄 Running weekly KPI calculations...")
        try:
            call_command('calculate_real_kpis', period='weekly', months=3)
            logger.info("✅ Weekly KPI calculations completed")
        except Exception as e:
            logger.error(f"❌ Weekly KPI calculation failed: {e}")

    def calculate_monthly_kpis(self):
        """Calculate monthly KPIs"""
        logger.info("🔄 Running monthly KPI calculations...")
        try:
            call_command('calculate_real_kpis', period='monthly', months=12)
            logger.info("✅ Monthly KPI calculations completed")
        except Exception as e:
            logger.error(f"❌ Monthly KPI calculation failed: {e}")

    def calculate_realtime_kpis(self):
        """Calculate real-time KPIs"""
        logger.info("🔄 Running real-time KPI calculations...")
        try:
            # Calculate only the most critical KPIs for real-time monitoring
            call_command('calculate_real_kpis', period='daily', months=1)
            logger.info("✅ Real-time KPI calculations completed")
        except Exception as e:
            logger.error(f"❌ Real-time KPI calculation failed: {e}")

    def show_status(self):
        """Show scheduler status and recent calculations"""
        self.stdout.write(
            self.style.SUCCESS('📊 KPI Calculation Status')
        )
        self.stdout.write('=' * 50)
        
        # Show scheduled jobs
        jobs = schedule.get_jobs()
        self.stdout.write(f'\n🕒 Scheduled Jobs: {len(jobs)}')
        for job in jobs:
            self.stdout.write(f'   • {job}')
        
        # Show recent KPI calculations
        recent_kpis = KPIMetricValue.objects.order_by('-calculated_at')[:10]
        self.stdout.write(f'\n📈 Recent KPI Calculations: {recent_kpis.count()}')
        for kpi in recent_kpis:
            self.stdout.write(
                f'   • {kpi.kpi_metric.name}: {kpi.value} '
                f'({kpi.calculated_at.strftime("%Y-%m-%d %H:%M")})'
            )
        
        # Show KPI metrics status
        total_metrics = KPIMetric.objects.count()
        active_metrics = KPIMetric.objects.filter(is_active=True).count()
        total_values = KPIMetricValue.objects.count()
        
        self.stdout.write(f'\n📊 KPI Metrics Overview:')
        self.stdout.write(f'   📋 Total Metrics: {total_metrics}')
        self.stdout.write(f'   ✅ Active Metrics: {active_metrics}')
        self.stdout.write(f'   📈 Total Values: {total_values}')
        
        # Show data freshness
        latest_calculation = KPIMetricValue.objects.order_by('-calculated_at').first()
        if latest_calculation:
            time_diff = timezone.now() - latest_calculation.calculated_at
            hours_ago = time_diff.total_seconds() / 3600
            self.stdout.write(f'   🕒 Latest Calculation: {hours_ago:.1f} hours ago')
        
        self.stdout.write('\n' + '=' * 50)


class KPISchedulerService:
    """Service class for managing KPI calculation scheduling"""
    
    @staticmethod
    def start_background_scheduler():
        """Start the KPI scheduler in background"""
        def run_scheduler():
            while True:
                try:
                    schedule.run_pending()
                    time.sleep(60)
                except Exception as e:
                    logger.error(f"Scheduler error: {e}")
                    time.sleep(300)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        return scheduler_thread
    
    @staticmethod
    def calculate_kpis_now():
        """Trigger immediate KPI calculation"""
        try:
            call_command('calculate_real_kpis', period='monthly', months=3)
            return True
        except Exception as e:
            logger.error(f"Manual KPI calculation failed: {e}")
            return False
    
    @staticmethod
    def get_kpi_status():
        """Get current KPI calculation status"""
        return {
            'total_metrics': KPIMetric.objects.count(),
            'active_metrics': KPIMetric.objects.filter(is_active=True).count(),
            'total_values': KPIMetricValue.objects.count(),
            'latest_calculation': KPIMetricValue.objects.order_by('-calculated_at').first(),
            'scheduled_jobs': len(schedule.get_jobs())
        }
