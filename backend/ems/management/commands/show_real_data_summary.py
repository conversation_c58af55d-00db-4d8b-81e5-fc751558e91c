"""
Management command to show summary of real business data created
This displays a comprehensive overview of the real data foundation
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum, Avg
from decimal import Decimal

from ems.models import (
    Employee, Department, Customer, Vendor, 
    Project, Task, Expense, CustomerInvoice, VendorInvoice,
    Attendance, LeaveRequest, LeaveType, KPIMetric, KPIMetricValue
)


class Command(BaseCommand):
    help = 'Show summary of real business data created for KPI calculations'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('📊 REAL BUSINESS DATA SUMMARY')
        )
        self.stdout.write('=' * 60)
        
        # Business Foundation
        self.show_business_foundation()
        
        # Operational Data
        self.show_operational_data()
        
        # Financial Data
        self.show_financial_data()
        
        # KPI Data
        self.show_kpi_data()
        
        # Data Quality Metrics
        self.show_data_quality()

    def show_business_foundation(self):
        """Show business foundation data"""
        self.stdout.write('\n🏢 BUSINESS FOUNDATION')
        self.stdout.write('-' * 30)
        
        # Departments
        departments = Department.objects.filter(is_active=True)
        total_budget = departments.aggregate(total=Sum('budget_amount'))['total'] or 0
        
        self.stdout.write(f'📋 Departments: {departments.count()}')
        for dept in departments:
            budget_str = f'{dept.budget_amount:,}' if dept.budget_amount else 'Not Set'
            self.stdout.write(f'   • {dept.name} (Budget: {budget_str} SAR)')
        self.stdout.write(f'   💰 Total Budget: {total_budget:,} SAR')
        
        # Employees
        employees = Employee.objects.filter(is_active=True)
        total_salary = employees.aggregate(total=Sum('salary'))['total'] or 0
        avg_salary = employees.aggregate(avg=Avg('salary'))['avg'] or 0
        
        self.stdout.write(f'\n👥 Employees: {employees.count()}')
        self.stdout.write(f'   💵 Total Monthly Salaries: {total_salary:,} SAR')
        self.stdout.write(f'   📊 Average Salary: {avg_salary:,.0f} SAR')
        
        # Department breakdown
        dept_breakdown = employees.values('department__name').annotate(
            count=Count('id'),
            avg_salary=Avg('salary')
        ).order_by('-count')
        
        for dept in dept_breakdown:
            if dept['department__name']:
                avg_salary = dept["avg_salary"] or 0
                self.stdout.write(
                    f'   • {dept["department__name"]}: {dept["count"]} employees '
                    f'(Avg: {avg_salary:,.0f} SAR)'
                )
        
        # Customers & Vendors
        customers = Customer.objects.filter(status='active')
        vendors = Vendor.objects.filter(is_active=True)
        
        self.stdout.write(f'\n🤝 Customers: {customers.count()}')
        self.stdout.write(f'🏭 Vendors: {vendors.count()}')

    def show_operational_data(self):
        """Show operational data"""
        self.stdout.write('\n🚀 OPERATIONAL DATA')
        self.stdout.write('-' * 30)
        
        # Projects
        projects = Project.objects.all()
        active_projects = projects.filter(status__in=['IN_PROGRESS', 'PLANNING'])
        completed_projects = projects.filter(status='COMPLETED')
        total_project_budget = projects.aggregate(total=Sum('budget_amount'))['total'] or 0
        
        self.stdout.write(f'📊 Projects: {projects.count()} total')
        self.stdout.write(f'   🟢 Active: {active_projects.count()}')
        self.stdout.write(f'   ✅ Completed: {completed_projects.count()}')
        self.stdout.write(f'   💰 Total Budget: {total_project_budget:,} SAR')
        
        # Tasks
        tasks = Task.objects.all()
        task_stats = tasks.values('status').annotate(count=Count('id'))
        
        self.stdout.write(f'\n📋 Tasks: {tasks.count()} total')
        for stat in task_stats:
            self.stdout.write(f'   • {stat["status"]}: {stat["count"]}')
        
        # Attendance
        attendance = Attendance.objects.all()
        present_count = attendance.filter(is_present=True).count()
        attendance_rate = (present_count / attendance.count() * 100) if attendance.count() > 0 else 0
        
        self.stdout.write(f'\n⏰ Attendance Records: {attendance.count()}')
        self.stdout.write(f'   📈 Attendance Rate: {attendance_rate:.1f}%')
        
        # Leave Requests
        leave_requests = LeaveRequest.objects.all()
        approved_leaves = leave_requests.filter(status='APPROVED').count()
        
        self.stdout.write(f'\n🏖️ Leave Requests: {leave_requests.count()}')
        self.stdout.write(f'   ✅ Approved: {approved_leaves}')

    def show_financial_data(self):
        """Show financial data"""
        self.stdout.write('\n💰 FINANCIAL DATA')
        self.stdout.write('-' * 30)
        
        # Customer Invoices
        customer_invoices = CustomerInvoice.objects.all()
        total_revenue = customer_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        paid_revenue = customer_invoices.filter(status='PAID').aggregate(total=Sum('paid_amount'))['total'] or 0
        
        self.stdout.write(f'📄 Customer Invoices: {customer_invoices.count()}')
        self.stdout.write(f'   💵 Total Revenue: {total_revenue:,} SAR')
        self.stdout.write(f'   ✅ Paid Revenue: {paid_revenue:,} SAR')
        
        # Vendor Invoices
        vendor_invoices = VendorInvoice.objects.all()
        total_payable = vendor_invoices.aggregate(total=Sum('total_amount'))['total'] or 0
        paid_payable = vendor_invoices.filter(status='PAID').aggregate(total=Sum('paid_amount'))['total'] or 0
        
        self.stdout.write(f'\n🧾 Vendor Invoices: {vendor_invoices.count()}')
        self.stdout.write(f'   💸 Total Payable: {total_payable:,} SAR')
        self.stdout.write(f'   ✅ Paid Amount: {paid_payable:,} SAR')
        
        # Expenses
        expenses = Expense.objects.all()
        approved_expenses = expenses.filter(status='APPROVED')
        total_expenses = approved_expenses.aggregate(total=Sum('amount'))['total'] or 0
        
        self.stdout.write(f'\n💳 Employee Expenses: {expenses.count()}')
        self.stdout.write(f'   ✅ Approved: {approved_expenses.count()}')
        self.stdout.write(f'   💰 Total Amount: {total_expenses:,} SAR')
        
        # Cash Flow Summary
        net_cash_flow = paid_revenue - paid_payable - total_expenses
        self.stdout.write(f'\n📊 NET CASH FLOW: {net_cash_flow:,} SAR')

    def show_kpi_data(self):
        """Show KPI calculation results"""
        self.stdout.write('\n📈 KPI CALCULATIONS')
        self.stdout.write('-' * 30)
        
        # KPI Metrics
        kpi_metrics = KPIMetric.objects.all()
        kpi_values = KPIMetricValue.objects.all()
        
        self.stdout.write(f'📊 KPI Metrics Defined: {kpi_metrics.count()}')
        self.stdout.write(f'📈 KPI Values Calculated: {kpi_values.count()}')
        
        # Recent KPI Values
        recent_values = kpi_values.order_by('-calculated_at')[:10]
        
        self.stdout.write('\n🔥 Recent KPI Calculations:')
        for kpi_value in recent_values:
            self.stdout.write(
                f'   • {kpi_value.kpi_metric.name}: {kpi_value.value} '
                f'({kpi_value.period_start} to {kpi_value.period_end})'
            )

    def show_data_quality(self):
        """Show data quality metrics"""
        self.stdout.write('\n✅ DATA QUALITY METRICS')
        self.stdout.write('-' * 30)
        
        # Employee data completeness
        employees = Employee.objects.filter(is_active=True)
        employees_with_dept = employees.exclude(department=None).count()
        employees_with_salary = employees.exclude(salary=None).count()
        
        dept_completeness = (employees_with_dept / employees.count() * 100) if employees.count() > 0 else 0
        salary_completeness = (employees_with_salary / employees.count() * 100) if employees.count() > 0 else 0
        
        self.stdout.write(f'👥 Employee Data Quality:')
        self.stdout.write(f'   📋 Department Assignment: {dept_completeness:.1f}%')
        self.stdout.write(f'   💰 Salary Information: {salary_completeness:.1f}%')
        
        # Project data completeness
        projects = Project.objects.all()
        projects_with_manager = projects.exclude(project_manager=None).count()
        projects_with_budget = projects.exclude(budget_amount=None).count()
        
        manager_completeness = (projects_with_manager / projects.count() * 100) if projects.count() > 0 else 0
        budget_completeness = (projects_with_budget / projects.count() * 100) if projects.count() > 0 else 0
        
        self.stdout.write(f'\n📊 Project Data Quality:')
        self.stdout.write(f'   👨‍💼 Manager Assignment: {manager_completeness:.1f}%')
        self.stdout.write(f'   💰 Budget Information: {budget_completeness:.1f}%')
        
        # Data freshness
        latest_attendance = Attendance.objects.order_by('-date').first()
        latest_kpi = KPIMetricValue.objects.order_by('-calculated_at').first()
        
        self.stdout.write(f'\n🕒 Data Freshness:')
        if latest_attendance:
            self.stdout.write(f'   ⏰ Latest Attendance: {latest_attendance.date}')
        if latest_kpi:
            self.stdout.write(f'   📈 Latest KPI Calculation: {latest_kpi.calculated_at.date()}')
        
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(
            self.style.SUCCESS('🎉 REAL BUSINESS DATA FOUNDATION COMPLETE!')
        )
        self.stdout.write(
            self.style.SUCCESS('Ready for automatic KPI calculations and business intelligence!')
        )
