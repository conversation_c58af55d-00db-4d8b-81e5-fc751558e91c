"""
Management command to set up the complete advanced KPI system
This orchestrates all the KPI enhancements and creates a production-ready system
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.utils import timezone
import time


class Command(BaseCommand):
    help = 'Set up complete advanced KPI system with all enhancements'

    def add_arguments(self, parser):
        parser.add_argument(
            '--full-setup',
            action='store_true',
            help='Run complete setup including data expansion'
        )
        parser.add_argument(
            '--skip-data',
            action='store_true',
            help='Skip data creation, only setup KPIs and scheduling'
        )

    def handle(self, *args, **options):
        full_setup = options['full_setup']
        skip_data = options['skip_data']
        
        self.stdout.write(
            self.style.SUCCESS('🚀 Setting up Advanced KPI System')
        )
        self.stdout.write('=' * 60)
        
        try:
            # Step 1: Expand KPI definitions
            self.stdout.write('\n📊 Step 1: Expanding KPI Definitions...')
            call_command('expand_kpi_definitions', category='all')
            
            # Step 2: Add more business data (if not skipped)
            if not skip_data:
                self.stdout.write('\n📈 Step 2: Expanding Business Data...')
                call_command('expand_business_data', scenario='growth', months=6, scale=1.5)
            
            # Step 3: Calculate KPIs with expanded data
            self.stdout.write('\n🔄 Step 3: Calculating KPIs...')
            call_command('calculate_real_kpis', period='monthly', months=6)
            
            # Step 4: Set up automated scheduling
            self.stdout.write('\n⏰ Step 4: Setting up KPI Scheduling...')
            call_command('schedule_kpi_calculations', mode='setup')
            
            # Step 5: Generate comprehensive summary
            self.stdout.write('\n📋 Step 5: Generating System Summary...')
            call_command('show_real_data_summary')
            
            # Step 6: Display next steps
            self.show_next_steps(full_setup)
            
            self.stdout.write('\n' + '=' * 60)
            self.stdout.write(
                self.style.SUCCESS('🎉 Advanced KPI System Setup Complete!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Setup failed: {str(e)}')
            )
            raise

    def show_next_steps(self, full_setup):
        """Display next steps for the user"""
        self.stdout.write('\n🎯 NEXT STEPS:')
        self.stdout.write('-' * 30)
        
        self.stdout.write('\n1. 📊 Access KPI Dashboard:')
        self.stdout.write('   • GET /api/kpi/dashboard/ - Overview dashboard')
        self.stdout.write('   • GET /api/kpi/dashboard/financial/ - Financial KPIs')
        self.stdout.write('   • GET /api/kpi/dashboard/hr/ - HR KPIs')
        self.stdout.write('   • GET /api/kpi/dashboard/operational/ - Operational KPIs')
        
        self.stdout.write('\n2. ⏰ Start Automated Scheduling:')
        self.stdout.write('   python manage.py schedule_kpi_calculations --mode run --daemon')
        
        self.stdout.write('\n3. 📈 Add More Data Scenarios:')
        self.stdout.write('   python manage.py expand_business_data --scenario seasonal')
        self.stdout.write('   python manage.py expand_business_data --scenario expansion')
        
        self.stdout.write('\n4. 🔄 Manual KPI Calculation:')
        self.stdout.write('   python manage.py calculate_real_kpis --period monthly --months 12')
        
        self.stdout.write('\n5. 📋 Check System Status:')
        self.stdout.write('   python manage.py schedule_kpi_calculations --mode status')
        self.stdout.write('   python manage.py show_real_data_summary')
        
        if full_setup:
            self.stdout.write('\n6. 🎨 Frontend Integration:')
            self.stdout.write('   • Update dashboard components to use new KPI APIs')
            self.stdout.write('   • Add real-time KPI widgets')
            self.stdout.write('   • Implement KPI alert notifications')
        
        self.stdout.write('\n7. 🔧 Production Deployment:')
        self.stdout.write('   • Set up cron jobs for KPI calculations')
        self.stdout.write('   • Configure monitoring and alerting')
        self.stdout.write('   • Set up backup for KPI data')


class KPISystemManager:
    """Utility class for managing the KPI system"""
    
    @staticmethod
    def get_system_status():
        """Get comprehensive system status"""
        from ems.models import KPIMetric, KPIMetricValue, Employee, Project
        
        return {
            'kpi_metrics': {
                'total': KPIMetric.objects.count(),
                'active': KPIMetric.objects.filter(is_active=True).count(),
                'categories': {
                    'financial': KPIMetric.objects.filter(
                        name__icontains='revenue'
                    ).count() + KPIMetric.objects.filter(
                        name__icontains='cash'
                    ).count(),
                    'hr': KPIMetric.objects.filter(
                        name__icontains='employee'
                    ).count() + KPIMetric.objects.filter(
                        name__icontains='turnover'
                    ).count(),
                    'operational': KPIMetric.objects.filter(
                        name__icontains='project'
                    ).count() + KPIMetric.objects.filter(
                        name__icontains='utilization'
                    ).count()
                }
            },
            'kpi_values': {
                'total': KPIMetricValue.objects.count(),
                'last_24h': KPIMetricValue.objects.filter(
                    calculated_at__gte=timezone.now() - timezone.timedelta(days=1)
                ).count(),
                'last_week': KPIMetricValue.objects.filter(
                    calculated_at__gte=timezone.now() - timezone.timedelta(days=7)
                ).count()
            },
            'business_data': {
                'employees': Employee.objects.filter(is_active=True).count(),
                'projects': Project.objects.count(),
                'active_projects': Project.objects.filter(
                    status__in=['IN_PROGRESS', 'PLANNING']
                ).count()
            },
            'last_updated': timezone.now().isoformat()
        }
    
    @staticmethod
    def trigger_full_calculation():
        """Trigger full KPI calculation"""
        try:
            call_command('calculate_real_kpis', period='monthly', months=12)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_kpi_health_check():
        """Get KPI system health check"""
        from ems.models import KPIMetric, KPIMetricValue
        
        health_status = {
            'status': 'healthy',
            'issues': [],
            'recommendations': []
        }
        
        # Check if KPIs have recent values
        stale_kpis = []
        for kpi in KPIMetric.objects.filter(is_active=True):
            latest_value = KPIMetricValue.objects.filter(
                kpi_metric=kpi
            ).order_by('-calculated_at').first()
            
            if not latest_value:
                stale_kpis.append(kpi.name)
            elif latest_value.calculated_at < timezone.now() - timezone.timedelta(days=7):
                stale_kpis.append(kpi.name)
        
        if stale_kpis:
            health_status['status'] = 'warning'
            health_status['issues'].append(f'Stale KPIs: {", ".join(stale_kpis)}')
            health_status['recommendations'].append('Run KPI calculations')
        
        # Check for KPIs without target values
        kpis_without_targets = KPIMetric.objects.filter(
            is_active=True,
            target_value__isnull=True
        ).count()
        
        if kpis_without_targets > 0:
            health_status['recommendations'].append(
                f'Set target values for {kpis_without_targets} KPIs'
            )
        
        return health_status
