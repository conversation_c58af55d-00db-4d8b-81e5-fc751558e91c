from rest_framework import serializers
from django.contrib.auth.models import User
from django.utils import timezone
from .models import (
    Department, Employee, Activity, Role, UserProfile, LeaveType, LeaveRequest,
    Attendance, Project, Task, Budget, Expense, AssetCategory, Asset, Supplier,
    PurchaseOrder, Announcement, Message, Document, Meeting, Customer,
    ProductCategory, Product, Report, SalesOrder, KPICategory, KPI, KPIValue, KPITarget, KPIAlert, Workflow,
    Tenant, TenantUser,
    # New Financial Models
    Currency, ExchangeRate, AccountType, ChartOfAccounts, FiscalYear, JournalEntryBatch, JournalEntry,
    Vendor, VendorInvoice, CustomerInvoice, Payment,

    # Enhanced Asset Management Models
    AssetCategory, Asset, AssetDepreciation, AssetMaintenance, AssetTransfer, AssetAudit,

    # Advanced Reporting & Analytics Models
    KPIMetric, KPIMetricValue, ReportTemplate, ReportExecution, Dashboard, AnalyticsQuery,

    # Integration & API Management Models
    APIKey, ExternalService, WebhookEndpoint, WebhookEvent, IntegrationLog,

    # Security & Compliance Models
    UserSecurityProfile, AuditTrail, SecurityIncident,
    ComplianceFramework, ComplianceControl, DataClassification, SecurityAlert
)

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined']

class DepartmentSerializer(serializers.ModelSerializer):
    employee_count = serializers.SerializerMethodField()
    manager_name = serializers.SerializerMethodField()
    manager_name_ar = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar',
                 'manager', 'manager_name', 'manager_name_ar', 'is_active',
                 'employee_count', 'created_at', 'updated_at']

    def get_employee_count(self, obj):
        return obj.employee_set.filter(is_active=True).count()

    def get_manager_name(self, obj):
        if obj.manager:
            return f"{obj.manager.user.first_name} {obj.manager.user.last_name}"
        return None

    def get_manager_name_ar(self, obj):
        if obj.manager and hasattr(obj.manager, 'first_name_ar'):
            return f"{getattr(obj.manager, 'first_name_ar', obj.manager.user.first_name)} {getattr(obj.manager, 'last_name_ar', obj.manager.user.last_name)}"
        elif obj.manager:
            return f"{obj.manager.user.first_name} {obj.manager.user.last_name}"
        return None

class EmployeeSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    department_name_ar = serializers.CharField(source='department.name_ar', read_only=True)

    # Add user profile with role information
    user_profile = serializers.SerializerMethodField()

    # Add created by information
    created_by_info = serializers.SerializerMethodField()

    # Fields for creating user
    first_name = serializers.CharField(write_only=True, required=False)
    last_name = serializers.CharField(write_only=True, required=False)
    email = serializers.EmailField(write_only=True, required=False)
    username = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Employee
        fields = ['id', 'user', 'employee_id', 'department', 'department_name',
                 'department_name_ar', 'position', 'position_ar', 'first_name_ar', 'last_name_ar',
                 'phone', 'gender', 'hire_date', 'salary', 'is_active', 'created_at', 'updated_at',
                 'first_name', 'last_name', 'email', 'username', 'user_profile', 'created_by_info',
                 'emergency_contact', 'emergency_phone']

    def get_user_profile(self, obj):
        """Get user profile with role information"""
        try:
            from .models import UserProfile
            user_profile = UserProfile.objects.select_related('role').get(user=obj.user)
            if user_profile.role:
                return {
                    'role': {
                        'id': user_profile.role.id,
                        'name': user_profile.role.name,
                        'name_ar': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions
                    }
                }
            return {'role': None}
        except UserProfile.DoesNotExist:
            return {'role': None}

    def get_created_by_info(self, obj):
        """Get information about who created this employee"""
        if obj.created_by:
            try:
                from .models import UserProfile
                creator_profile = UserProfile.objects.select_related('role').get(user=obj.created_by.user)
                return {
                    'id': obj.created_by.id,
                    'name': f"{obj.created_by.user.first_name} {obj.created_by.user.last_name}".strip(),
                    'nameAr': f"{obj.created_by.first_name_ar} {obj.created_by.last_name_ar}".strip() if obj.created_by.first_name_ar else '',
                    'role': creator_profile.role.name if creator_profile.role else 'EMPLOYEE',
                    'roleAr': creator_profile.role.name_ar if creator_profile.role else 'موظف'
                }
            except UserProfile.DoesNotExist:
                return {
                    'id': obj.created_by.id,
                    'name': f"{obj.created_by.user.first_name} {obj.created_by.user.last_name}".strip(),
                    'nameAr': f"{obj.created_by.first_name_ar} {obj.created_by.last_name_ar}".strip() if obj.created_by.first_name_ar else '',
                    'role': 'EMPLOYEE',
                    'roleAr': 'موظف'
                }
        return None

    def get_department_name(self, obj):
        """Get department name"""
        return obj.department.name if obj.department else None

    def get_department_name_ar(self, obj):
        """Get department name in Arabic"""
        return obj.department.name_ar if obj.department else None

    def create(self, validated_data):
        # Extract user data
        first_name = validated_data.pop('first_name', '')
        last_name = validated_data.pop('last_name', '')
        email = validated_data.pop('email', '')
        username = validated_data.pop('username', '')

        # If no username provided, generate one from employee_id or email
        if not username:
            username = validated_data.get('employee_id', email.split('@')[0] if email else f"user_{validated_data.get('employee_id', '')}")

        # Create User first (inactive by default for new employees)
        user = User.objects.create_user(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_active=False  # New employees start inactive until they activate their account
        )

        # Note: created_by is now handled in the viewset's perform_create method
        # to avoid duplicate logic and ensure proper error handling

        # Create Employee with the user
        validated_data['user'] = user
        employee = Employee.objects.create(**validated_data)

        # Create activation record for new employee (pending admin approval)
        from .models import EmployeeActivation
        activation = EmployeeActivation.objects.create(
            employee=employee,
            approval_status='PENDING'  # Requires admin approval before activation email is sent
        )

        # Log that employee was created and is pending approval
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Employee {employee.employee_id} created and pending admin approval for activation")

        return employee

    def update(self, instance, validated_data):
        # Extract user data
        first_name = validated_data.pop('first_name', None)
        last_name = validated_data.pop('last_name', None)
        email = validated_data.pop('email', None)
        username = validated_data.pop('username', None)

        # Update user if user data provided
        if any([first_name, last_name, email, username]):
            user = instance.user
            if first_name is not None:
                user.first_name = first_name
            if last_name is not None:
                user.last_name = last_name
            if email is not None:
                user.email = email
            if username is not None:
                user.username = username
            user.save()

        # Update employee
        return super().update(instance, validated_data)

class ActivitySerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = Activity
        fields = ['id', 'user', 'user_name', 'activity_type', 'description',
                 'description_ar', 'timestamp', 'ip_address']

class DashboardStatsSerializer(serializers.Serializer):
    total_employees = serializers.IntegerField()
    total_departments = serializers.IntegerField()
    active_employees = serializers.IntegerField()
    recent_activities = ActivitySerializer(many=True)

# Role and User Profile Serializers
class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'permissions', 'created_at']

class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    role = RoleSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = ['id', 'user', 'role', 'avatar', 'phone', 'address', 'date_of_birth',
                 'emergency_contact_name', 'emergency_contact_phone', 'preferred_language',
                 'timezone', 'created_at', 'updated_at']

# HR Management Serializers
class LeaveTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveType
        fields = ['id', 'name', 'name_ar', 'days_allowed', 'is_paid', 'requires_approval',
                 'carry_forward', 'description', 'description_ar', 'is_active', 'created_at']

class LeaveRequestSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    leave_type_name = serializers.CharField(source='leave_type.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)

    class Meta:
        model = LeaveRequest
        fields = ['id', 'employee', 'employee_name', 'leave_type', 'leave_type_name',
                 'start_date', 'end_date', 'days_requested', 'reason', 'reason_ar',
                 'status', 'approved_by', 'approved_by_name', 'approval_date',
                 'rejection_reason', 'rejection_reason_ar', 'created_at', 'updated_at']

class EmployeeLeaveSerializer(serializers.ModelSerializer):
    """Serializer for Employee Leave - transforms LeaveRequest data to match frontend interface"""

    # Frontend fields (for output)
    type = serializers.SerializerMethodField(read_only=True)
    typeAr = serializers.SerializerMethodField(read_only=True)
    startDate = serializers.SerializerMethodField(read_only=True)
    endDate = serializers.SerializerMethodField(read_only=True)
    duration = serializers.SerializerMethodField(read_only=True)
    reason = serializers.SerializerMethodField(read_only=True)
    reasonAr = serializers.SerializerMethodField(read_only=True)
    requestDate = serializers.SerializerMethodField(read_only=True)
    approvedBy = serializers.SerializerMethodField(read_only=True)
    approvedByAr = serializers.SerializerMethodField(read_only=True)
    comments = serializers.SerializerMethodField(read_only=True)
    commentsAr = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = LeaveRequest
        fields = [
            'id', 'type', 'typeAr', 'startDate', 'endDate', 'duration',
            'reason', 'reasonAr', 'status', 'requestDate', 'approvedBy',
            'approvedByAr', 'comments', 'commentsAr'
        ]
        extra_kwargs = {
            'status': {'required': False}
        }

    def get_type(self, obj):
        """Map leave_type to simple string"""
        # Map common leave types to frontend expected values
        type_mapping = {
            'Annual Leave': 'annual',
            'Sick Leave': 'sick',
            'Emergency Leave': 'emergency',
            'Maternity Leave': 'maternity',
            'Paternity Leave': 'paternity',
            'Unpaid Leave': 'unpaid'
        }
        return type_mapping.get(obj.leave_type.name, 'annual')

    def get_typeAr(self, obj):
        """Get Arabic leave type name"""
        return obj.leave_type.name_ar if hasattr(obj.leave_type, 'name_ar') else obj.leave_type.name

    def get_requestDate(self, obj):
        """Get request date as string"""
        return obj.created_at.strftime('%Y-%m-%d')

    def get_approvedBy(self, obj):
        """Get approver name"""
        if obj.approved_by:
            return obj.approved_by.user.get_full_name()
        elif obj.status.upper() == 'PENDING':
            return "قيد المراجعة"  # "Under Review" in Arabic
        else:
            return "-"

    def get_approvedByAr(self, obj):
        """Get approver name in Arabic"""
        if obj.approved_by:
            return obj.approved_by.user.get_full_name()
        elif obj.status.upper() == 'PENDING':
            return "قيد المراجعة"  # "Under Review" in Arabic
        else:
            return "-"

    def get_startDate(self, obj):
        """Get start date as string"""
        return obj.start_date.strftime('%Y-%m-%d')

    def get_endDate(self, obj):
        """Get end date as string"""
        return obj.end_date.strftime('%Y-%m-%d')

    def get_duration(self, obj):
        """Get duration (days_requested)"""
        return obj.days_requested

    def get_reason(self, obj):
        """Get reason"""
        if obj.reason and obj.reason.strip():
            return obj.reason
        elif obj.reason_ar and obj.reason_ar.strip():
            return obj.reason_ar  # Fallback to Arabic reason if English is empty
        else:
            return ""  # Return empty string instead of None

    def get_reasonAr(self, obj):
        """Get Arabic reason"""
        if obj.reason_ar and obj.reason_ar.strip():
            return obj.reason_ar
        elif obj.reason and obj.reason.strip():
            return obj.reason  # Fallback to English reason if Arabic is empty
        else:
            return ""  # Return empty string instead of None

    def get_comments(self, obj):
        """Get comments (rejection_reason)"""
        return obj.rejection_reason

    def get_commentsAr(self, obj):
        """Get Arabic comments (rejection_reason_ar)"""
        return obj.rejection_reason_ar

    def to_representation(self, instance):
        """Transform backend data to frontend format"""
        data = super().to_representation(instance)

        # Convert status from uppercase to lowercase for frontend
        if 'status' in data and data['status']:
            data['status'] = data['status'].lower()

        return data

    def to_internal_value(self, data):
        """Override to handle frontend data format"""
        # Just pass through the data as-is since we handle transformation in create()
        # We only need to validate the required fields exist
        required_fields = ['startDate', 'endDate', 'duration', 'reason']
        for field in required_fields:
            if field not in data:
                raise serializers.ValidationError(f"Field '{field}' is required")

        return data

    def create(self, validated_data):
        """Handle creation with frontend data transformation"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"EmployeeLeaveSerializer.create - Validated data: {validated_data}")

        # Get the current user's employee record
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("Request context not available")

        try:
            employee = Employee.objects.get(user=request.user)
        except Employee.DoesNotExist:
            raise serializers.ValidationError("Employee record not found for current user")

        # Get leave type based on user selection
        type_mapping = {
            'annual': 'Annual Leave',
            'sick': 'Sick Leave',
            'emergency': 'Emergency Leave',
            'maternity': 'Maternity Leave',
            'paternity': 'Paternity Leave',
            'unpaid': 'Unpaid Leave'
        }

        selected_type = validated_data.get('type')
        if not selected_type:
            raise serializers.ValidationError("Leave type is required")

        type_name = type_mapping.get(selected_type)
        if not type_name:
            raise serializers.ValidationError(f"Invalid leave type: {selected_type}")

        try:
            leave_type = LeaveType.objects.get(name=type_name, is_active=True)
        except LeaveType.DoesNotExist:
            raise serializers.ValidationError(f"Leave type '{type_name}' not found or inactive")

        # Parse dates
        from datetime import datetime
        try:
            start_date = datetime.strptime(validated_data.get('startDate'), '%Y-%m-%d').date()
            end_date = datetime.strptime(validated_data.get('endDate'), '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            raise serializers.ValidationError(f"Invalid date format: {str(e)}")

        # Create the leave request with proper data
        leave_request = LeaveRequest.objects.create(
            employee=employee,
            leave_type=leave_type,
            start_date=start_date,
            end_date=end_date,
            days_requested=validated_data.get('duration'),
            reason=validated_data.get('reason', ''),
            reason_ar=validated_data.get('reasonAr', ''),
            status='PENDING'
        )

        logger.info(f"Successfully created leave request: {leave_request}")
        return leave_request

class AttendanceSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = Attendance
        fields = ['id', 'employee', 'employee_name', 'date', 'check_in', 'check_out',
                 'break_start', 'break_end', 'total_hours', 'overtime_hours',
                 'is_present', 'is_late', 'notes', 'created_at', 'updated_at']

# Project Management Serializers
class ProjectSerializer(serializers.ModelSerializer):
    project_manager_name = serializers.CharField(source='project_manager.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    team_members_count = serializers.SerializerMethodField()
    tasks_count = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar',
                 'project_manager', 'project_manager_name', 'department', 'department_name',
                 'team_members', 'team_members_count', 'client', 'budget_amount',
                 'start_date', 'end_date', 'actual_start_date', 'actual_end_date',
                 'status', 'priority', 'progress_percentage', 'tasks_count',
                 'is_active', 'created_at', 'updated_at']

    def get_team_members_count(self, obj):
        return obj.team_members.count()

    def get_tasks_count(self, obj):
        return obj.tasks.count()

class TaskSerializer(serializers.ModelSerializer):
    project_name = serializers.CharField(source='project.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = Task
        fields = ['id', 'project', 'project_name', 'title', 'title_ar', 'description',
                 'description_ar', 'assigned_to', 'assigned_to_name', 'created_by',
                 'created_by_name', 'due_date', 'start_date', 'completion_date',
                 'estimated_hours', 'actual_hours', 'status', 'priority',
                 'progress_percentage', 'dependencies', 'tags', 'created_at', 'updated_at']

# Financial Management Serializers
class BudgetSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = Budget
        fields = ['id', 'name', 'name_ar', 'department', 'department_name',
                 'project', 'project_name', 'fiscal_year', 'total_amount',
                 'allocated_amount', 'spent_amount', 'remaining_amount',
                 'start_date', 'end_date', 'is_active', 'created_by',
                 'created_by_name', 'created_at', 'updated_at']

class ExpenseSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    budget_name = serializers.CharField(source='budget.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)

    class Meta:
        model = Expense
        fields = ['id', 'employee', 'employee_name', 'budget', 'budget_name',
                 'project', 'project_name', 'title', 'title_ar', 'description',
                 'description_ar', 'category', 'amount', 'currency', 'expense_date',
                 'receipt_file', 'status', 'approved_by', 'approved_by_name',
                 'approval_date', 'rejection_reason', 'created_at', 'updated_at']

# Asset Management Serializers
class AssetCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetCategory
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'created_at']

class AssetSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    assigned_to_name = serializers.SerializerMethodField()

    def get_assigned_to_name(self, obj):
        if obj.assigned_to and obj.assigned_to.user:
            return obj.assigned_to.user.get_full_name()
        return None

    class Meta:
        model = Asset
        fields = ['id', 'asset_id', 'name', 'name_ar', 'category', 'category_name',
                 'description', 'description_ar', 'serial_number', 'model',
                 'manufacturer', 'purchase_date', 'purchase_price', 'current_value',
                 'warranty_expiry', 'assigned_to', 'assigned_to_name', 'location',
                 'status', 'notes', 'created_at', 'updated_at']

class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = ['id', 'name', 'name_ar', 'contact_person', 'email', 'phone',
                 'address', 'website', 'tax_number', 'payment_terms', 'is_active',
                 'created_at', 'updated_at']

class PurchaseOrderSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)

    class Meta:
        model = PurchaseOrder
        fields = ['id', 'po_number', 'supplier', 'supplier_name', 'requested_by',
                 'requested_by_name', 'approved_by', 'approved_by_name', 'department',
                 'department_name', 'project', 'project_name', 'order_date',
                 'expected_delivery', 'actual_delivery', 'total_amount', 'currency',
                 'status', 'notes', 'created_at', 'updated_at']

# Communication & Collaboration Serializers
class AnnouncementSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.user.get_full_name', read_only=True)
    target_departments_names = serializers.SerializerMethodField()
    target_employees_count = serializers.SerializerMethodField()

    class Meta:
        model = Announcement
        fields = ['id', 'title', 'title_ar', 'content', 'content_ar', 'author',
                 'author_name', 'target_departments', 'target_departments_names',
                 'target_employees', 'target_employees_count', 'priority',
                 'is_published', 'publish_date', 'expiry_date', 'attachment',
                 'created_at', 'updated_at']

    def get_target_departments_names(self, obj):
        return [dept.name for dept in obj.target_departments.all()]

    def get_target_employees_count(self, obj):
        return obj.target_employees.count()

class MessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.user.get_full_name', read_only=True)
    recipient_name = serializers.CharField(source='recipient.user.get_full_name', read_only=True)

    class Meta:
        model = Message
        fields = ['id', 'sender', 'sender_name', 'recipient', 'recipient_name',
                 'subject', 'content', 'is_read', 'is_important', 'parent_message',
                 'attachment', 'sent_at', 'read_at']
        read_only_fields = ['sender', 'sent_at', 'read_at']  # FIXED: Make sender read-only so it's set by perform_create

class DocumentSerializer(serializers.ModelSerializer):
    uploaded_by_name = serializers.CharField(source='uploaded_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    access_permissions_count = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = ['id', 'title', 'title_ar', 'description', 'description_ar',
                 'category', 'file', 'version', 'uploaded_by', 'uploaded_by_name',
                 'department', 'department_name', 'is_public', 'access_permissions',
                 'access_permissions_count', 'tags', 'download_count',
                 'created_at', 'updated_at']

    def get_access_permissions_count(self, obj):
        return obj.access_permissions.count()

class MeetingSerializer(serializers.ModelSerializer):
    organizer_name = serializers.CharField(source='organizer.user.get_full_name', read_only=True)
    attendees_names = serializers.SerializerMethodField()
    attendees_count = serializers.SerializerMethodField()

    class Meta:
        model = Meeting
        fields = ['id', 'title', 'title_ar', 'description', 'description_ar',
                 'organizer', 'organizer_name', 'attendees', 'attendees_names',
                 'attendees_count', 'start_time', 'end_time', 'location',
                 'meeting_link', 'agenda', 'agenda_ar', 'minutes', 'minutes_ar',
                 'status', 'is_recurring', 'recurrence_pattern', 'created_at', 'updated_at']

    def get_attendees_names(self, obj):
        return [emp.user.get_full_name() for emp in obj.attendees.all()]

    def get_attendees_count(self, obj):
        return obj.attendees.count()

class PersonalCalendarSerializer(serializers.ModelSerializer):
    """Serializer for Personal Calendar - transforms Meeting data to match frontend CalendarEvent interface"""

    # Transform Meeting fields to match CalendarEvent interface
    titleAr = serializers.CharField(source='title_ar', allow_blank=True)
    descriptionAr = serializers.CharField(source='description_ar', allow_blank=True)
    date = serializers.SerializerMethodField()
    time = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()
    locationAr = serializers.CharField(source='location', allow_blank=True)  # Use same location for Arabic
    attendees = serializers.SerializerMethodField()
    attendeesAr = serializers.SerializerMethodField()
    isOnline = serializers.SerializerMethodField()
    priority = serializers.SerializerMethodField()
    createdBy = serializers.SerializerMethodField()
    createdByAr = serializers.SerializerMethodField()

    class Meta:
        model = Meeting
        fields = [
            'id', 'title', 'titleAr', 'description', 'descriptionAr',
            'date', 'time', 'duration', 'type', 'location', 'locationAr',
            'attendees', 'attendeesAr', 'isOnline', 'priority', 'status',
            'createdBy', 'createdByAr'
        ]

    def get_date(self, obj):
        """Extract date from start_time"""
        return obj.start_time.strftime('%Y-%m-%d')

    def get_time(self, obj):
        """Extract time from start_time"""
        return obj.start_time.strftime('%H:%M')

    def get_duration(self, obj):
        """Calculate duration from start_time and end_time"""
        if obj.end_time and obj.start_time:
            duration = obj.end_time - obj.start_time
            hours = duration.seconds // 3600
            minutes = (duration.seconds % 3600) // 60
            if hours > 0:
                return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
            else:
                return f"{minutes}m"
        return "1h"  # Default duration

    def get_type(self, obj):
        """Map meeting to calendar event type"""
        return 'meeting'  # All meetings are of type 'meeting'

    def get_attendees(self, obj):
        """Get attendees as newline-separated string"""
        return '\n'.join([emp.user.get_full_name() for emp in obj.attendees.all()])

    def get_attendeesAr(self, obj):
        """Get attendees in Arabic (same as English for now)"""
        return self.get_attendees(obj)

    def get_isOnline(self, obj):
        """Check if meeting is online based on meeting_link"""
        return bool(obj.meeting_link)

    def get_priority(self, obj):
        """Map status to priority"""
        priority_map = {
            'SCHEDULED': 'medium',
            'IN_PROGRESS': 'high',
            'COMPLETED': 'low',
            'CANCELLED': 'low',
            'POSTPONED': 'medium'
        }
        return priority_map.get(obj.status, 'medium')

    def get_createdBy(self, obj):
        """Get organizer name"""
        return obj.organizer.user.get_full_name()

    def get_createdByAr(self, obj):
        """Get organizer name in Arabic (same as English for now)"""
        return self.get_createdBy(obj)

# Customer Management Serializers
class CustomerSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = [
            'id', 'customer_id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'customer_type', 'status', 'company_name', 'company_size', 'industry',
            'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
            'source', 'internal_notes', 'tags', 'total_orders', 'total_spent',
            'satisfaction_score', 'created_at', 'updated_at', 'last_contact_date'
        ]
        read_only_fields = ['customer_id', 'created_at', 'updated_at']

    def get_full_name(self, obj):
        return obj.get_full_name()

# Product Management Serializers
class ProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'is_active', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'name_ar', 'sku', 'description', 'description_ar',
            'category', 'category_name', 'brand', 'brand_ar', 'unit_price', 'cost_price',
            'quantity_in_stock', 'minimum_stock_level', 'maximum_stock_level', 'reorder_point',
            'unit_of_measure', 'unit_of_measure_ar', 'barcode', 'weight', 'dimensions',
            'status', 'supplier', 'supplier_name', 'location', 'location_ar',
            'expiry_date', 'batch_number', 'created_at', 'updated_at'
        ]

# Report Management Serializers
class ReportSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = Report
        fields = [
            'id', 'name', 'name_ar', 'type', 'description', 'description_ar',
            'status', 'created_date', 'completed_date', 'file_size', 'file_url',
            'parameters', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_date', 'completed_date', 'created_by']

# Sales Management Serializers
class SalesOrderSerializer(serializers.ModelSerializer):
    customer_name = serializers.SerializerMethodField()
    customer_id = serializers.IntegerField(source='customer.id', read_only=True)

    class Meta:
        model = SalesOrder
        fields = [
            'id', 'order_number', 'customer', 'customer_id', 'customer_name',
            'total_amount', 'status', 'priority', 'order_date', 'delivery_date',
            'items_count', 'discount', 'tax', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'customer_id', 'customer_name']
        depth = 0  # Prevent nested serialization

    def get_customer_name(self, obj):
        if obj.customer:
            return obj.customer.get_full_name()
        return None

    def to_representation(self, instance):
        # Get the standard representation
        data = super().to_representation(instance)
        # Ensure customer field is just the ID, not the full object
        if 'customer' in data and isinstance(data['customer'], dict):
            data['customer'] = instance.customer.id if instance.customer else None
        return data

# KPI Serializers
class KPICategorySerializer(serializers.ModelSerializer):
    kpi_count = serializers.SerializerMethodField()

    class Meta:
        model = KPICategory
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'icon', 'color', 'is_active', 'sort_order', 'kpi_count',
            'created_at', 'updated_at'
        ]

    def get_kpi_count(self, obj):
        return obj.kpis.filter(status='ACTIVE').count()


class KPISerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.get_name_display', read_only=True)
    category_name_ar = serializers.CharField(source='category.name_ar', read_only=True)
    owner_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    current_value = serializers.SerializerMethodField()
    target_achievement = serializers.SerializerMethodField()
    trend = serializers.SerializerMethodField()

    class Meta:
        model = KPI
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'category', 'category_name', 'category_name_ar',
            'measurement_type', 'unit', 'unit_ar', 'frequency', 'trend_direction',
            'formula', 'data_source', 'calculation_method',
            'target_value', 'warning_threshold', 'critical_threshold',
            'visible_to_roles', 'owner', 'owner_name',
            'status', 'is_automated', 'automation_config',
            'created_by', 'created_by_name', 'created_at', 'updated_at',
            'current_value', 'target_achievement', 'trend'
        ]

    def get_current_value(self, obj):
        """Get the most recent KPI value"""
        # First try to use the KPI model's current_value field
        if obj.current_value is not None:
            return {
                'value': float(obj.current_value),
                'period_start': None,
                'period_end': None,
                'recorded_at': obj.last_updated or obj.updated_at
            }

        # Fallback to latest KPI value record (ordered by most recent)
        latest_value = obj.values.order_by('-period_end', '-recorded_at').first()
        if latest_value:
            return {
                'value': float(latest_value.value),
                'period_start': latest_value.period_start,
                'period_end': latest_value.period_end,
                'recorded_at': latest_value.recorded_at
            }
        return None

    def get_target_achievement(self, obj):
        """Calculate achievement percentage against target"""
        current_value = self.get_current_value(obj)
        if current_value and obj.target_value:
            achievement = (current_value['value'] / float(obj.target_value)) * 100
            return round(achievement, 2)
        return None

    def get_trend(self, obj):
        """Calculate trend based on recent values"""
        recent_values = obj.values.order_by('-period_start')[:2]
        if len(recent_values) >= 2:
            current = float(recent_values[0].value)
            previous = float(recent_values[1].value)

            # Avoid division by zero
            if previous == 0:
                if current == 0:
                    change = 0
                else:
                    # If previous was 0 and current is not, it's a 100% increase
                    change = 100 if current > 0 else -100
            else:
                change = ((current - previous) / previous) * 100

            return {
                'change_percentage': round(change, 2),
                'direction': 'up' if change > 0 else 'down' if change < 0 else 'stable'
            }
        return None

    def get_owner_name(self, obj):
        """Get owner name safely"""
        if obj.owner and obj.owner.user:
            return obj.owner.user.get_full_name()
        return None

    def get_created_by_name(self, obj):
        """Get created by name safely"""
        if obj.created_by and obj.created_by.user:
            return obj.created_by.user.get_full_name()
        return None


class KPIValueSerializer(serializers.ModelSerializer):
    """
    DEPRECATED: Read-only KPI Value Serializer.
    Manual KPI value creation has been disabled to enforce enterprise automation.
    All KPI values must be calculated automatically from operational data.
    """
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    kpi_unit = serializers.CharField(source='kpi.unit', read_only=True)
    recorded_by_name = serializers.CharField(source='recorded_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = KPIValue
        fields = [
            'id', 'kpi', 'kpi_name', 'kpi_unit', 'value',
            'period_start', 'period_end',
            'department', 'department_name',
            'project', 'project_name',
            'employee', 'employee_name',
            'data_quality_score', 'confidence_level', 'notes',
            'recorded_by', 'recorded_by_name', 'recorded_at',
            'is_estimated', 'source_data'
        ]
        read_only_fields = ['__all__']  # Make all fields read-only

    def create(self, validated_data):
        """Prevent manual KPI value creation"""
        # Unused parameter is intentional - required by DRF interface
        _ = validated_data
        from rest_framework.exceptions import PermissionDenied
        raise PermissionDenied(
            "Manual KPI value creation is disabled. "
            "KPI values are calculated automatically from operational data. "
            "Use the enhanced KPI calculation engine instead."
        )

    def update(self, instance, validated_data):
        """Prevent manual KPI value updates"""
        # Unused parameters are intentional - required by DRF interface
        _ = instance
        _ = validated_data
        from rest_framework.exceptions import PermissionDenied
        raise PermissionDenied(
            "Manual KPI value updates are disabled. "
            "KPI values are calculated automatically from operational data. "
            "Use the enhanced KPI calculation engine instead."
        )


class KPITargetSerializer(serializers.ModelSerializer):
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = KPITarget
        fields = [
            'id', 'kpi', 'kpi_name', 'target_value', 'target_type',
            'start_date', 'end_date',
            'department', 'department_name',
            'project', 'project_name',
            'employee', 'employee_name',
            'description', 'is_stretch_goal', 'weight',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class KPIAlertSerializer(serializers.ModelSerializer):
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.user.get_full_name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.user.get_full_name', read_only=True)

    class Meta:
        model = KPIAlert
        fields = [
            'id', 'kpi', 'kpi_name', 'alert_type', 'severity',
            'title', 'title_ar', 'message', 'message_ar',
            'current_value', 'threshold_value', 'target_value',
            'status', 'acknowledged_by', 'acknowledged_by_name', 'acknowledged_at',
            'resolved_by', 'resolved_by_name', 'resolved_at', 'resolution_notes',
            'notified_users', 'notification_sent',
            'created_at', 'updated_at'
        ]


class KPIDashboardSerializer(serializers.Serializer):
    """Serializer for KPI dashboard data"""
    categories = KPICategorySerializer(many=True, read_only=True)
    recent_alerts = KPIAlertSerializer(many=True, read_only=True)
    top_performing_kpis = KPISerializer(many=True, read_only=True)
    underperforming_kpis = KPISerializer(many=True, read_only=True)
    kpi_summary = serializers.DictField(read_only=True)


class KPISummarySerializer(serializers.Serializer):
    """Summary statistics for KPIs"""
    total_kpis = serializers.IntegerField()
    active_kpis = serializers.IntegerField()
    kpis_on_target = serializers.IntegerField()
    kpis_above_target = serializers.IntegerField()
    kpis_below_target = serializers.IntegerField()
    active_alerts = serializers.IntegerField()
    critical_alerts = serializers.IntegerField()
    categories_count = serializers.IntegerField()
    last_updated = serializers.DateTimeField()


class KPITrendDataSerializer(serializers.Serializer):
    """Serializer for KPI trend data"""
    kpi_id = serializers.UUIDField()
    kpi_name = serializers.CharField()
    data_points = serializers.ListField(
        child=serializers.DictField()
    )
    trend_direction = serializers.CharField()
    change_percentage = serializers.FloatField()


class RoleBasedKPISerializer(serializers.Serializer):
    """Role-based KPI data for dashboards"""
    role = serializers.CharField()
    accessible_kpis = KPISerializer(many=True, read_only=True)
    role_specific_metrics = serializers.DictField(read_only=True)
    alerts = KPIAlertSerializer(many=True, read_only=True)

class WorkflowSerializer(serializers.ModelSerializer):
    created_by_name = serializers.SerializerMethodField()
    success_rate = serializers.ReadOnlyField()
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = Workflow
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'category', 'category_ar', 'category_display', 'status', 'status_display',
            'priority', 'priority_display', 'trigger_event', 'conditions', 'conditions_ar',
            'actions', 'actions_ar', 'is_automated', 'next_run', 'last_run',
            'run_count', 'success_count', 'success_rate', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.user.first_name} {obj.created_by.user.last_name}"
        return None


# Multi-Tenant Serializers
class TenantUserSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = TenantUser
        fields = [
            'id', 'user', 'user_name', 'role', 'permissions',
            'is_active', 'last_login', 'created_at'
        ]


class TenantSerializer(serializers.ModelSerializer):
    tenant_users = TenantUserSerializer(many=True, read_only=True)
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Tenant
        fields = [
            'id', 'name', 'domain', 'subdomain', 'plan', 'status',
            'settings', 'limits', 'features', 'billing', 'metadata',
            'created_at', 'updated_at', 'tenant_users', 'user_count'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_user_count(self, obj):
        return obj.tenant_users.filter(is_active=True).count()


class TenantUsageSerializer(serializers.Serializer):
    """Serializer for tenant usage statistics"""
    users = serializers.DictField()
    storage = serializers.DictField()
    api_calls = serializers.DictField()
    projects = serializers.DictField()


class TenantBillingSerializer(serializers.Serializer):
    """Serializer for tenant billing information"""
    plan = serializers.CharField()
    billing_cycle = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField()
    next_billing_date = serializers.DateTimeField()
    payment_method = serializers.CharField()
    invoices = serializers.ListField()
    usage = TenantUsageSerializer()


# Financial Management Serializers - General Ledger
class AccountTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountType
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'is_active', 'created_at']

class ChartOfAccountsSerializer(serializers.ModelSerializer):
    account_type_name = serializers.CharField(source='account_type.get_name_display', read_only=True)
    parent_account_name = serializers.CharField(source='parent_account.account_name', read_only=True)
    full_code = serializers.CharField(source='get_full_code', read_only=True)
    current_balance = serializers.SerializerMethodField()
    sub_accounts_count = serializers.SerializerMethodField()

    class Meta:
        model = ChartOfAccounts
        fields = [
            'id', 'account_code', 'account_name', 'account_name_ar', 'account_type',
            'account_type_name', 'parent_account', 'parent_account_name', 'description',
            'description_ar', 'is_active', 'is_system_account', 'level', 'full_code',
            'allow_manual_entries', 'require_department', 'require_project',
            'current_balance', 'sub_accounts_count', 'created_at', 'updated_at'
        ]

    def get_current_balance(self, obj):
        return float(obj.get_balance())

    def get_sub_accounts_count(self, obj):
        return obj.sub_accounts.count()

class FiscalYearSerializer(serializers.ModelSerializer):
    class Meta:
        model = FiscalYear
        fields = ['id', 'name', 'start_date', 'end_date', 'is_current', 'is_closed', 'created_at']

class JournalEntryBatchSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    posted_by_name = serializers.CharField(source='posted_by.user.get_full_name', read_only=True)
    total_debits = serializers.SerializerMethodField()
    total_credits = serializers.SerializerMethodField()
    is_balanced = serializers.SerializerMethodField()
    entries_count = serializers.SerializerMethodField()

    class Meta:
        model = JournalEntryBatch
        fields = [
            'id', 'batch_number', 'description', 'description_ar', 'created_by',
            'created_by_name', 'created_at', 'is_posted', 'posted_at', 'posted_by',
            'posted_by_name', 'total_debits', 'total_credits', 'is_balanced', 'entries_count'
        ]

    def get_total_debits(self, obj):
        return float(obj.get_total_debits())

    def get_total_credits(self, obj):
        return float(obj.get_total_credits())

    def get_is_balanced(self, obj):
        return obj.is_balanced()

    def get_entries_count(self, obj):
        return obj.journal_entries.count()

class JournalEntrySerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_code = serializers.CharField(source='account.account_code', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = JournalEntry
        fields = [
            'id', 'batch', 'entry_number', 'account', 'account_name', 'account_code',
            'transaction_date', 'description', 'description_ar', 'debit_amount',
            'credit_amount', 'department', 'department_name', 'project', 'project_name',
            'employee', 'employee_name', 'source_document_type', 'source_document_id',
            'is_posted', 'posted_at', 'created_at', 'updated_at'
        ]


# Accounts Payable Serializers
class VendorSerializer(serializers.ModelSerializer):
    outstanding_balance = serializers.SerializerMethodField()
    invoices_count = serializers.SerializerMethodField()

    class Meta:
        model = Vendor
        fields = [
            'id', 'vendor_code', 'company_name', 'company_name_ar', 'contact_person',
            'email', 'phone', 'address', 'address_ar', 'tax_id', 'payment_terms',
            'credit_limit', 'currency', 'is_active', 'is_approved', 'outstanding_balance',
            'invoices_count', 'created_at', 'updated_at'
        ]

    def get_outstanding_balance(self, obj):
        return float(obj.get_outstanding_balance())

    def get_invoices_count(self, obj):
        return obj.vendor_invoices.count()

class VendorInvoiceSerializer(serializers.ModelSerializer):
    vendor_name = serializers.CharField(source='vendor.company_name', read_only=True)
    vendor_code = serializers.CharField(source='vendor.vendor_code', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    # Currency fields
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)

    # Calculated fields
    remaining_balance = serializers.SerializerMethodField()
    remaining_balance_base = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    days_overdue = serializers.SerializerMethodField()

    class Meta:
        model = VendorInvoice
        fields = [
            'id', 'invoice_number', 'vendor_invoice_number', 'vendor', 'vendor_name',
            'vendor_code', 'invoice_date', 'due_date', 'description', 'description_ar',
            # Currency fields
            'currency', 'currency_code', 'currency_symbol', 'currency_name', 'exchange_rate',
            # Amounts in invoice currency
            'subtotal', 'tax_amount', 'discount_amount', 'total_amount', 'paid_amount',
            # Amounts in base currency
            'subtotal_base', 'tax_amount_base', 'discount_amount_base', 'total_amount_base', 'paid_amount_base',
            # Calculated fields
            'remaining_balance', 'remaining_balance_base', 'purchase_order', 'department', 'department_name',
            'project', 'project_name', 'status', 'approved_by', 'approved_by_name',
            'approval_date', 'invoice_file', 'created_by', 'created_by_name',
            'is_overdue', 'days_overdue', 'created_at', 'updated_at'
        ]

    def get_remaining_balance(self, obj):
        return float(obj.get_remaining_balance())

    def get_remaining_balance_base(self, obj):
        return float(obj.get_remaining_balance_base())

    def get_is_overdue(self, obj):
        return obj.is_overdue()

    def get_days_overdue(self, obj):
        if obj.is_overdue():
            return (timezone.now().date() - obj.due_date).days
        return 0

class CustomerInvoiceSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    # Currency fields
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)

    # Calculated fields
    remaining_balance = serializers.SerializerMethodField()
    remaining_balance_base = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    days_overdue = serializers.SerializerMethodField()

    class Meta:
        model = CustomerInvoice
        fields = [
            'id', 'invoice_number', 'customer', 'customer_name', 'invoice_date',
            'due_date', 'description', 'description_ar',
            # Currency fields
            'currency', 'currency_code', 'currency_symbol', 'currency_name', 'exchange_rate',
            # Amounts in invoice currency
            'subtotal', 'tax_amount', 'discount_amount', 'total_amount', 'paid_amount',
            # Amounts in base currency
            'subtotal_base', 'tax_amount_base', 'discount_amount_base', 'total_amount_base', 'paid_amount_base',
            # Calculated fields
            'remaining_balance', 'remaining_balance_base', 'sales_order', 'department', 'department_name',
            'project', 'project_name', 'status', 'created_by', 'created_by_name',
            'is_overdue', 'days_overdue', 'created_at', 'updated_at'
        ]

    def get_remaining_balance(self, obj):
        return float(obj.get_remaining_balance())

    def get_remaining_balance_base(self, obj):
        return float(obj.get_remaining_balance_base())

    def get_is_overdue(self, obj):
        return obj.is_overdue()

    def get_days_overdue(self, obj):
        if obj.is_overdue():
            return (timezone.now().date() - obj.due_date).days
        return 0

class CurrencySerializer(serializers.ModelSerializer):
    """Serializer for Currency model"""

    class Meta:
        model = Currency
        fields = [
            'id', 'code', 'name', 'symbol', 'decimal_places',
            'is_base_currency', 'is_active', 'symbol_position',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class ExchangeRateSerializer(serializers.ModelSerializer):
    """Serializer for ExchangeRate model"""
    from_currency_code = serializers.CharField(source='from_currency.code', read_only=True)
    to_currency_code = serializers.CharField(source='to_currency.code', read_only=True)
    from_currency_name = serializers.CharField(source='from_currency.name', read_only=True)
    to_currency_name = serializers.CharField(source='to_currency.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = ExchangeRate
        fields = [
            'id', 'from_currency', 'to_currency', 'rate', 'effective_date',
            'source', 'is_active', 'from_currency_code', 'to_currency_code',
            'from_currency_name', 'to_currency_name', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by_name']


class AssetCategorySerializer(serializers.ModelSerializer):
    """Serializer for AssetCategory model"""

    class Meta:
        model = AssetCategory
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'created_at']
        read_only_fields = ['created_at']


class AssetSerializer(serializers.ModelSerializer):
    """Enhanced serializer for Asset model with depreciation and maintenance info"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    # Currency fields
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)

    # Calculated fields
    current_book_value = serializers.SerializerMethodField()
    accumulated_depreciation = serializers.SerializerMethodField()
    annual_depreciation = serializers.SerializerMethodField()
    is_under_warranty = serializers.SerializerMethodField()
    days_until_warranty_expiry = serializers.SerializerMethodField()
    is_maintenance_due = serializers.SerializerMethodField()

    # Status indicators
    age_in_years = serializers.SerializerMethodField()
    depreciation_percentage = serializers.SerializerMethodField()

    class Meta:
        model = Asset
        fields = [
            'id', 'asset_id', 'name', 'name_ar', 'category', 'category_name',
            'description', 'description_ar', 'serial_number', 'model', 'manufacturer',
            'barcode', 'qr_code',
            # Financial
            'currency', 'currency_code', 'currency_symbol', 'purchase_date', 'purchase_price',
            'purchase_price_base', 'current_value', 'salvage_value',
            # Depreciation
            'depreciation_method', 'useful_life_years', 'useful_life_units', 'depreciation_rate',
            'current_book_value', 'accumulated_depreciation', 'annual_depreciation',
            'age_in_years', 'depreciation_percentage',
            # Warranty and Maintenance
            'warranty_expiry', 'warranty_provider', 'maintenance_schedule',
            'last_maintenance_date', 'next_maintenance_date', 'is_under_warranty',
            'days_until_warranty_expiry', 'is_maintenance_due',
            # Assignment
            'assigned_to', 'assigned_to_name', 'department', 'department_name',
            'location', 'building', 'floor', 'room',
            # Status
            'status', 'condition',
            # Additional
            'supplier', 'supplier_name', 'purchase_order', 'invoice_number',
            'insurance_policy', 'insurance_expiry',
            # Disposal
            'disposal_date', 'disposal_method', 'disposal_value', 'disposal_reason',
            # System
            'notes', 'tags', 'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'purchase_price_base']

    def get_current_book_value(self, obj):
        return float(obj.get_current_book_value())

    def get_accumulated_depreciation(self, obj):
        return float(obj.get_accumulated_depreciation())

    def get_annual_depreciation(self, obj):
        return float(obj.get_annual_depreciation())

    def get_is_under_warranty(self, obj):
        return obj.is_under_warranty()

    def get_days_until_warranty_expiry(self, obj):
        return obj.days_until_warranty_expiry()

    def get_is_maintenance_due(self, obj):
        return obj.is_maintenance_due()

    def get_age_in_years(self, obj):
        from django.utils import timezone
        delta = timezone.now().date() - obj.purchase_date
        return round(delta.days / 365.25, 1)

    def get_depreciation_percentage(self, obj):
        if obj.purchase_price == 0:
            return 0
        accumulated = obj.get_accumulated_depreciation()
        return round((accumulated / obj.purchase_price) * 100, 1)


class AssetDepreciationSerializer(serializers.ModelSerializer):
    """Serializer for AssetDepreciation model"""
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_id = serializers.CharField(source='asset.asset_id', read_only=True)
    calculated_by_name = serializers.CharField(source='calculated_by.user.get_full_name', read_only=True)

    class Meta:
        model = AssetDepreciation
        fields = [
            'id', 'asset', 'asset_name', 'asset_id', 'period_start', 'period_end',
            'depreciation_amount', 'accumulated_depreciation', 'book_value',
            'method_used', 'calculation_notes', 'calculated_by', 'calculated_by_name',
            'calculated_at', 'is_adjustment', 'adjustment_reason'
        ]
        read_only_fields = ['calculated_at']


class AssetMaintenanceSerializer(serializers.ModelSerializer):
    """Serializer for AssetMaintenance model"""
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_id = serializers.CharField(source='asset.asset_id', read_only=True)
    performed_by_name = serializers.CharField(source='performed_by.user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)

    # Calculated fields
    is_overdue = serializers.SerializerMethodField()
    duration_actual = serializers.SerializerMethodField()
    cost_variance = serializers.SerializerMethodField()

    class Meta:
        model = AssetMaintenance
        fields = [
            'id', 'asset', 'asset_name', 'asset_id', 'maintenance_type', 'title', 'description',
            'scheduled_date', 'scheduled_time', 'estimated_duration',
            'actual_start_date', 'actual_end_date', 'duration_actual',
            'performed_by', 'performed_by_name', 'external_vendor',
            'status', 'priority', 'is_overdue',
            'estimated_cost', 'actual_cost', 'cost_variance', 'currency', 'currency_code',
            'work_performed', 'parts_used', 'issues_found', 'recommendations',
            'next_maintenance_date', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_is_overdue(self, obj):
        return obj.is_overdue()

    def get_duration_actual(self, obj):
        if obj.actual_start_date and obj.actual_end_date:
            delta = obj.actual_end_date - obj.actual_start_date
            return str(delta)
        return None

    def get_cost_variance(self, obj):
        if obj.estimated_cost and obj.actual_cost:
            return float(obj.actual_cost - obj.estimated_cost)
        return None


class AssetTransferSerializer(serializers.ModelSerializer):
    """Serializer for AssetTransfer model"""
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_id = serializers.CharField(source='asset.asset_id', read_only=True)
    from_employee_name = serializers.CharField(source='from_employee.user.get_full_name', read_only=True)
    to_employee_name = serializers.CharField(source='to_employee.user.get_full_name', read_only=True)
    from_department_name = serializers.CharField(source='from_department.name', read_only=True)
    to_department_name = serializers.CharField(source='to_department.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)

    class Meta:
        model = AssetTransfer
        fields = [
            'id', 'asset', 'asset_name', 'asset_id', 'transfer_type',
            'from_employee', 'from_employee_name', 'from_department', 'from_department_name', 'from_location',
            'to_employee', 'to_employee_name', 'to_department', 'to_department_name', 'to_location',
            'transfer_date', 'reason', 'notes', 'condition_before', 'condition_after',
            'status', 'requested_by', 'requested_by_name', 'approved_by', 'approved_by_name',
            'approval_date', 'rejection_reason', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AssetAuditSerializer(serializers.ModelSerializer):
    """Serializer for AssetAudit model"""
    asset_name = serializers.CharField(source='asset.name', read_only=True)
    asset_id = serializers.CharField(source='asset.asset_id', read_only=True)
    expected_assignee_name = serializers.CharField(source='expected_assignee.user.get_full_name', read_only=True)
    actual_assignee_name = serializers.CharField(source='actual_assignee.user.get_full_name', read_only=True)
    audited_by_name = serializers.CharField(source='audited_by.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', read_only=True)

    class Meta:
        model = AssetAudit
        fields = [
            'id', 'asset', 'asset_name', 'asset_id', 'audit_type', 'audit_date',
            'expected_location', 'actual_location', 'expected_assignee', 'expected_assignee_name',
            'actual_assignee', 'actual_assignee_name', 'expected_condition', 'actual_condition',
            'status', 'result', 'discrepancy_notes', 'corrective_action',
            'audited_by', 'audited_by_name', 'verified_by', 'verified_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class KPIMetricSerializer(serializers.ModelSerializer):
    """Serializer for KPIMetric model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    current_value = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    trend = serializers.SerializerMethodField()
    achievement_percentage = serializers.SerializerMethodField()
    last_updated = serializers.SerializerMethodField()

    class Meta:
        model = KPIMetric
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'metric_type', 'calculation_method', 'custom_formula',
            'target_value', 'warning_threshold', 'critical_threshold',
            'unit', 'decimal_places', 'is_higher_better', 'frequency',
            'is_active', 'created_by', 'created_by_name', 'current_value',
            'status', 'trend', 'achievement_percentage', 'last_updated',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_current_value(self, obj):
        """Get the most recent KPI value"""
        try:
            latest_value = obj.metric_values.order_by('-period_end').first()
            return float(latest_value.value) if latest_value else 0.0
        except Exception:
            return 0.0

    def get_status(self, obj):
        """Calculate KPI status based on current value vs target"""
        try:
            current_value = self.get_current_value(obj)
            if not obj.target_value or current_value == 0:
                return 'Unknown'

            target = float(obj.target_value)
            achievement_ratio = current_value / target

            # For metrics where higher is better
            if obj.is_higher_better:
                if achievement_ratio >= 0.9:  # Within 90% of target or above
                    return 'Good'
                elif achievement_ratio >= 0.7:  # Within 70% of target
                    return 'Warning'
                else:
                    return 'Critical'
            else:
                # For metrics where lower is better (like costs, turnover)
                if achievement_ratio <= 1.1:  # Within 110% of target or below
                    return 'Good'
                elif achievement_ratio <= 1.3:  # Within 130% of target
                    return 'Warning'
                else:
                    return 'Critical'
        except Exception:
            return 'Unknown'

    def get_trend(self, obj):
        """Calculate trend based on recent values"""
        try:
            recent_values = obj.metric_values.order_by('-period_end')[:3]
            if len(recent_values) < 2:
                return 'stable'

            latest = float(recent_values[0].value)
            previous = float(recent_values[1].value)

            if latest > previous * 1.05:  # 5% increase
                return 'up'
            elif latest < previous * 0.95:  # 5% decrease
                return 'down'
            else:
                return 'stable'
        except Exception:
            return 'stable'

    def get_achievement_percentage(self, obj):
        """Calculate achievement percentage vs target"""
        try:
            current_value = self.get_current_value(obj)
            if not obj.target_value or current_value == 0:
                return 0.0

            target = float(obj.target_value)
            return round((current_value / target) * 100, 1)
        except Exception:
            return 0.0

    def get_last_updated(self, obj):
        """Get the last updated timestamp"""
        try:
            latest_value = obj.metric_values.order_by('-period_end').first()
            return latest_value.calculated_at.isoformat() if latest_value else None
        except Exception:
            return None


class KPIMetricValueSerializer(serializers.ModelSerializer):
    """Serializer for KPIMetricValue model"""
    kpi_name = serializers.CharField(source='kpi_metric.name', read_only=True)
    kpi_unit = serializers.CharField(source='kpi_metric.unit', read_only=True)
    calculated_by_name = serializers.CharField(source='calculated_by.user.get_full_name', read_only=True)
    status = serializers.SerializerMethodField()

    class Meta:
        model = KPIMetricValue
        fields = [
            'id', 'kpi_metric', 'kpi_name', 'kpi_unit', 'period_start', 'period_end',
            'value', 'status', 'notes', 'data_source', 'calculated_by',
            'calculated_by_name', 'calculated_at', 'is_manual'
        ]
        read_only_fields = ['calculated_at']

    def get_status(self, obj):
        return obj.get_status()


class ReportTemplateSerializer(serializers.ModelSerializer):
    """Serializer for ReportTemplate model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    execution_count = serializers.SerializerMethodField()
    last_executed = serializers.SerializerMethodField()

    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'report_type', 'template_config', 'data_sources', 'default_filters',
            'output_formats', 'page_orientation', 'is_public', 'allowed_roles',
            'created_by', 'created_by_name', 'execution_count', 'last_executed',
            'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_execution_count(self, obj):
        return obj.executions.count()

    def get_last_executed(self, obj):
        latest_execution = obj.executions.first()
        return latest_execution.created_at if latest_execution else None


class ReportExecutionSerializer(serializers.ModelSerializer):
    """Serializer for ReportExecution model"""
    template_name = serializers.CharField(source='template.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.user.get_full_name', read_only=True)
    duration_seconds = serializers.SerializerMethodField()

    class Meta:
        model = ReportExecution
        fields = [
            'id', 'template', 'template_name', 'parameters', 'output_format',
            'status', 'started_at', 'completed_at', 'execution_time', 'duration_seconds',
            'output_file', 'file_size', 'error_message', 'requested_by',
            'requested_by_name', 'created_at'
        ]
        read_only_fields = ['created_at']

    def get_duration_seconds(self, obj):
        if obj.execution_time:
            return obj.execution_time.total_seconds()
        return None


class DashboardSerializer(serializers.ModelSerializer):
    """Serializer for Dashboard model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    widget_count = serializers.SerializerMethodField()

    class Meta:
        model = Dashboard
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'dashboard_type', 'layout_config', 'widgets', 'refresh_interval',
            'is_default', 'is_public', 'allowed_roles', 'department',
            'department_name', 'created_by', 'created_by_name', 'widget_count',
            'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_widget_count(self, obj):
        return len(obj.widgets) if obj.widgets else 0


class AnalyticsQuerySerializer(serializers.ModelSerializer):
    """Serializer for AnalyticsQuery model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = AnalyticsQuery
        fields = [
            'id', 'name', 'description', 'query_type', 'query_definition',
            'sql_query', 'cache_duration', 'last_executed', 'execution_count',
            'created_by', 'created_by_name', 'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['created_at', 'updated_at', 'last_executed', 'execution_count']


class APIKeySerializer(serializers.ModelSerializer):
    """Serializer for APIKey model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    is_valid_status = serializers.SerializerMethodField()
    days_until_expiry = serializers.SerializerMethodField()

    class Meta:
        model = APIKey
        fields = [
            'id', 'name', 'description', 'key_type', 'key_id', 'key_prefix',
            'scopes', 'allowed_ips', 'rate_limit', 'status', 'expires_at',
            'last_used_at', 'usage_count', 'is_valid_status', 'days_until_expiry',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'last_used_at', 'usage_count', 'key_secret']

    def get_is_valid_status(self, obj):
        return obj.is_valid()

    def get_days_until_expiry(self, obj):
        if not obj.expires_at:
            return None
        delta = obj.expires_at.date() - timezone.now().date()
        return delta.days if delta.days >= 0 else 0


class ExternalServiceSerializer(serializers.ModelSerializer):
    """Serializer for ExternalService model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    health_status = serializers.SerializerMethodField()

    class Meta:
        model = ExternalService
        fields = [
            'id', 'name', 'service_type', 'description', 'base_url', 'api_version',
            'authentication_type', 'config', 'headers', 'timeout', 'status',
            'last_health_check', 'health_check_url', 'is_healthy', 'success_rate',
            'total_requests', 'successful_requests', 'failed_requests', 'last_request_at',
            'health_status', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'total_requests', 'successful_requests',
            'failed_requests', 'last_request_at', 'last_health_check', 'credentials'
        ]

    def get_success_rate(self, obj):
        return round(obj.get_success_rate(), 2)

    def get_health_status(self, obj):
        if not obj.last_health_check:
            return 'UNKNOWN'

        # Consider service unhealthy if last check was more than 1 hour ago
        time_since_check = timezone.now() - obj.last_health_check
        if time_since_check.total_seconds() > 3600:
            return 'STALE'

        return 'HEALTHY' if obj.is_healthy else 'UNHEALTHY'


class WebhookEndpointSerializer(serializers.ModelSerializer):
    """Serializer for WebhookEndpoint model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    success_rate = serializers.SerializerMethodField()
    recent_events_count = serializers.SerializerMethodField()

    class Meta:
        model = WebhookEndpoint
        fields = [
            'id', 'name', 'description', 'event_types', 'url', 'method', 'headers',
            'max_retries', 'retry_delay', 'timeout', 'status', 'is_verified',
            'last_triggered_at', 'total_calls', 'successful_calls', 'failed_calls',
            'success_rate', 'recent_events_count', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'total_calls', 'successful_calls',
            'failed_calls', 'last_triggered_at', 'secret'
        ]

    def get_success_rate(self, obj):
        return round(obj.get_success_rate(), 2)

    def get_recent_events_count(self, obj):
        from datetime import timedelta
        recent_cutoff = timezone.now() - timedelta(hours=24)
        return obj.events.filter(created_at__gte=recent_cutoff).count()


class WebhookEventSerializer(serializers.ModelSerializer):
    """Serializer for WebhookEvent model"""
    webhook_name = serializers.CharField(source='webhook_endpoint.name', read_only=True)
    webhook_url = serializers.CharField(source='webhook_endpoint.url', read_only=True)
    can_retry_status = serializers.SerializerMethodField()
    duration_seconds = serializers.SerializerMethodField()

    class Meta:
        model = WebhookEvent
        fields = [
            'id', 'webhook_endpoint', 'webhook_name', 'webhook_url', 'event_type',
            'event_id', 'payload', 'headers', 'status', 'attempts', 'max_attempts',
            'response_status_code', 'response_body', 'response_headers',
            'scheduled_at', 'first_attempted_at', 'last_attempted_at', 'delivered_at',
            'error_message', 'can_retry_status', 'duration_seconds',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'first_attempted_at', 'last_attempted_at',
            'delivered_at'
        ]

    def get_can_retry_status(self, obj):
        return obj.can_retry()

    def get_duration_seconds(self, obj):
        if obj.first_attempted_at and obj.delivered_at:
            delta = obj.delivered_at - obj.first_attempted_at
            return delta.total_seconds()
        return None


class IntegrationLogSerializer(serializers.ModelSerializer):
    """Serializer for IntegrationLog model"""
    external_service_name = serializers.CharField(source='external_service.name', read_only=True)
    webhook_endpoint_name = serializers.CharField(source='webhook_endpoint.name', read_only=True)
    api_key_name = serializers.CharField(source='api_key.name', read_only=True)

    class Meta:
        model = IntegrationLog
        fields = [
            'id', 'log_type', 'severity', 'external_service', 'external_service_name',
            'webhook_endpoint', 'webhook_endpoint_name', 'api_key', 'api_key_name',
            'message', 'details', 'request_data', 'response_data', 'duration_ms',
            'created_at', 'ip_address', 'user_agent'
        ]
        read_only_fields = ['created_at']



class UserSecurityProfileSerializer(serializers.ModelSerializer):
    """Serializer for UserSecurityProfile model"""
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    username = serializers.CharField(source='employee.user.username', read_only=True)
    is_locked = serializers.SerializerMethodField()
    password_expires_soon = serializers.SerializerMethodField()

    class Meta:
        model = UserSecurityProfile
        fields = [
            'id', 'employee', 'employee_name', 'username', 'security_level',
            'mfa_enabled', 'mfa_method', 'password_expires_at', 'failed_login_attempts',
            'account_locked_until', 'is_locked', 'password_expires_soon',
            'max_concurrent_sessions', 'current_session_count', 'last_login_ip',
            'last_login_location', 'login_notifications', 'suspicious_activity_alerts',
            'data_access_notifications', 'security_training_completed',
            'privacy_policy_accepted', 'terms_accepted', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'mfa_secret', 'password_history']

    def get_is_locked(self, obj):
        return obj.is_account_locked()

    def get_password_expires_soon(self, obj):
        if not obj.password_expires_at:
            return False
        days_until_expiry = (obj.password_expires_at.date() - timezone.now().date()).days
        return days_until_expiry <= 7


class AuditTrailSerializer(serializers.ModelSerializer):
    """Serializer for AuditTrail model"""
    username = serializers.CharField(source='user.username', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = AuditTrail
        fields = [
            'id', 'action_type', 'action_description', 'risk_level', 'user',
            'username', 'employee', 'employee_name', 'session_id', 'resource_type',
            'resource_id', 'resource_name', 'old_values', 'new_values', 'changed_fields',
            'ip_address', 'user_agent', 'location', 'additional_context',
            'compliance_category', 'business_justification', 'timestamp'
        ]
        read_only_fields = ['timestamp']


class SecurityIncidentSerializer(serializers.ModelSerializer):
    """Serializer for SecurityIncident model"""
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)
    reported_by_name = serializers.CharField(source='reported_by.user.get_full_name', read_only=True)
    affected_user_count = serializers.SerializerMethodField()
    duration_hours = serializers.SerializerMethodField()

    class Meta:
        model = SecurityIncident
        fields = [
            'id', 'incident_id', 'title', 'description', 'incident_type', 'severity',
            'status', 'affected_users', 'affected_user_count', 'affected_systems',
            'data_categories_affected', 'detected_at', 'reported_at', 'contained_at',
            'resolved_at', 'duration_hours', 'root_cause', 'impact_assessment',
            'remediation_actions', 'regulatory_notification_required',
            'regulatory_notification_sent', 'customer_notification_required',
            'customer_notification_sent', 'assigned_to', 'assigned_to_name',
            'reported_by', 'reported_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'reported_at']

    def get_affected_user_count(self, obj):
        return obj.affected_users.count()

    def get_duration_hours(self, obj):
        duration = obj.get_duration()
        return round(duration.total_seconds() / 3600, 2) if duration else None


class ComplianceFrameworkSerializer(serializers.ModelSerializer):
    """Serializer for ComplianceFramework model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    control_count = serializers.SerializerMethodField()
    implemented_controls = serializers.SerializerMethodField()
    assessment_overdue = serializers.SerializerMethodField()

    class Meta:
        model = ComplianceFramework
        fields = [
            'id', 'name', 'name_ar', 'framework_type', 'description', 'description_ar',
            'version', 'effective_date', 'requirements', 'is_active',
            'implementation_status', 'compliance_score', 'control_count',
            'implemented_controls', 'last_assessment_date', 'next_assessment_due',
            'assessment_overdue', 'assessment_frequency_months', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_control_count(self, obj):
        return obj.controls.count()

    def get_implemented_controls(self, obj):
        return obj.controls.filter(status='IMPLEMENTED').count()

    def get_assessment_overdue(self, obj):
        return obj.is_assessment_due()


class ComplianceControlSerializer(serializers.ModelSerializer):
    """Serializer for ComplianceControl model"""
    framework_name = serializers.CharField(source='framework.name', read_only=True)
    control_owner_name = serializers.CharField(source='control_owner.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='responsible_department.name', read_only=True)
    test_overdue = serializers.SerializerMethodField()

    class Meta:
        model = ComplianceControl
        fields = [
            'id', 'framework', 'framework_name', 'control_id', 'name', 'name_ar',
            'description', 'description_ar', 'control_type', 'status',
            'implementation_notes', 'evidence_required', 'evidence_collected',
            'risk_level', 'priority', 'last_tested_date', 'test_frequency_months',
            'test_results', 'test_overdue', 'control_owner', 'control_owner_name',
            'responsible_department', 'department_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_test_overdue(self, obj):
        return obj.is_test_due()


class DataClassificationSerializer(serializers.ModelSerializer):
    """Serializer for DataClassification model"""
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    framework_count = serializers.SerializerMethodField()
    authorized_role_count = serializers.SerializerMethodField()

    class Meta:
        model = DataClassification
        fields = [
            'id', 'name', 'name_ar', 'classification_level', 'description',
            'description_ar', 'encryption_required', 'access_logging_required',
            'approval_required_for_access', 'retention_period', 'retention_unit',
            'disposal_method', 'applicable_frameworks', 'framework_count',
            'regulatory_requirements', 'authorized_roles', 'authorized_role_count',
            'geographic_restrictions', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_framework_count(self, obj):
        return obj.applicable_frameworks.count()

    def get_authorized_role_count(self, obj):
        return obj.authorized_roles.count()


class SecurityAlertSerializer(serializers.ModelSerializer):
    """Serializer for SecurityAlert model"""
    affected_user_name = serializers.CharField(source='affected_user.get_full_name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)
    escalated_to_name = serializers.CharField(source='escalated_to.user.get_full_name', read_only=True)
    response_time_minutes = serializers.SerializerMethodField()
    resolution_time_hours = serializers.SerializerMethodField()

    class Meta:
        model = SecurityAlert
        fields = [
            'id', 'alert_id', 'alert_type', 'title', 'description', 'severity',
            'status', 'source_system', 'source_ip', 'affected_user',
            'affected_user_name', 'affected_resource', 'detection_rules',
            'evidence', 'risk_score', 'assigned_to', 'assigned_to_name',
            'response_actions', 'resolution_notes', 'detected_at',
            'acknowledged_at', 'resolved_at', 'response_time_minutes',
            'resolution_time_hours', 'escalated', 'escalated_at',
            'escalated_to', 'escalated_to_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_response_time_minutes(self, obj):
        response_time = obj.get_response_time()
        return round(response_time.total_seconds() / 60, 2) if response_time else None

    def get_resolution_time_hours(self, obj):
        resolution_time = obj.get_resolution_time()
        return round(resolution_time.total_seconds() / 3600, 2) if resolution_time else None


class PaymentSerializer(serializers.ModelSerializer):
    vendor_invoice_number = serializers.CharField(source='vendor_invoice.invoice_number', read_only=True)
    customer_invoice_number = serializers.CharField(source='customer_invoice.invoice_number', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = Payment
        fields = [
            'id', 'payment_number', 'payment_type', 'payment_method', 'payment_date',
            'amount', 'currency', 'exchange_rate', 'vendor_invoice', 'vendor_invoice_number',
            'customer_invoice', 'customer_invoice_number', 'bank_account', 'check_number',
            'reference_number', 'description', 'description_ar', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
