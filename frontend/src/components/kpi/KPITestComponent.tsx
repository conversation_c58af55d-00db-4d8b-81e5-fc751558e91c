/**
 * KPI Test Component
 * Simple test component to verify KPI functionality
 */

import React from 'react'
import { BarChart3, TrendingUp, Users, Building } from 'lucide-react'

interface KPITestComponentProps {
  dashboardType?: string
  language?: 'ar' | 'en'
}

const KPITestComponent: React.FC<KPITestComponentProps> = ({ 
  dashboardType = 'executive',
  language = 'en' 
}) => {
  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-white mb-2">
          {language === 'ar' ? 'اختبار مؤشرات الأداء' : 'KPI Test Dashboard'}
        </h1>
        <p className="text-white/70">
          {language === 'ar' ? `نوع اللوحة: ${dashboardType}` : `Dashboard Type: ${dashboardType}`}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Test KPI Cards */}
        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <BarChart3 className="h-6 w-6 text-blue-400" />
            </div>
            <span className="text-green-400 text-sm">+12%</span>
          </div>
          <h3 className="text-white font-semibold mb-1">
            {language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}
          </h3>
          <p className="text-2xl font-bold text-white">$125,430</p>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-400" />
            </div>
            <span className="text-green-400 text-sm">+8%</span>
          </div>
          <h3 className="text-white font-semibold mb-1">
            {language === 'ar' ? 'النمو الشهري' : 'Monthly Growth'}
          </h3>
          <p className="text-2xl font-bold text-white">23.5%</p>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Users className="h-6 w-6 text-purple-400" />
            </div>
            <span className="text-green-400 text-sm">+5%</span>
          </div>
          <h3 className="text-white font-semibold mb-1">
            {language === 'ar' ? 'العملاء النشطون' : 'Active Users'}
          </h3>
          <p className="text-2xl font-bold text-white">1,234</p>
        </div>

        <div className="glass-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <Building className="h-6 w-6 text-orange-400" />
            </div>
            <span className="text-red-400 text-sm">-2%</span>
          </div>
          <h3 className="text-white font-semibold mb-1">
            {language === 'ar' ? 'الأقسام' : 'Departments'}
          </h3>
          <p className="text-2xl font-bold text-white">12</p>
        </div>
      </div>

      <div className="glass-card p-6">
        <h2 className="text-xl font-bold text-white mb-4">
          {language === 'ar' ? 'حالة النظام' : 'System Status'}
        </h2>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-white/70">
              {language === 'ar' ? 'حالة الاتصال' : 'Connection Status'}
            </span>
            <span className="text-green-400">✅ Connected</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-white/70">
              {language === 'ar' ? 'نوع اللوحة' : 'Dashboard Type'}
            </span>
            <span className="text-blue-400">{dashboardType}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-white/70">
              {language === 'ar' ? 'آخر تحديث' : 'Last Updated'}
            </span>
            <span className="text-white/70">{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default KPITestComponent
