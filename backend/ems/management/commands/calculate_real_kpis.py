"""
Management command to calculate KPIs from real business data
This automatically computes KPI values from actual operational data
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q
from decimal import Decimal
from datetime import datetime, timedelta

from ems.models import (
    KPIMetric, KPIMetricValue, Employee, Attendance, 
    CustomerInvoice, VendorInvoice, Expense, Project, Task,
    LeaveRequest, Department
)


class Command(BaseCommand):
    help = 'Calculate KPI values from real business data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--period',
            type=str,
            choices=['daily', 'weekly', 'monthly'],
            default='monthly',
            help='Calculation period'
        )
        parser.add_argument(
            '--months',
            type=int,
            default=6,
            help='Number of months to calculate'
        )

    def handle(self, *args, **options):
        self.period = options['period']
        self.months = options['months']
        
        self.stdout.write(
            self.style.SUCCESS(f'📊 Calculating KPIs from real data ({self.period} periods)...')
        )
        
        # Get the employee who will be marked as calculator
        calculator = Employee.objects.first()
        if not calculator:
            self.stdout.write(
                self.style.ERROR('❌ No employees found. Cannot calculate KPIs.')
            )
            return
        
        # Calculate different KPI types
        calculated_count = 0
        
        calculated_count += self.calculate_hr_kpis(calculator)
        calculated_count += self.calculate_financial_kpis(calculator)
        calculated_count += self.calculate_operational_kpis(calculator)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Calculated {calculated_count} KPI values from real data!')
        )

    def calculate_hr_kpis(self, calculator):
        """Calculate HR-related KPIs from real employee data"""
        calculated = 0
        
        # Employee Turnover Rate
        turnover_kpi = KPIMetric.objects.filter(name__icontains='turnover').first()
        if turnover_kpi:
            calculated += self.calculate_turnover_rate(turnover_kpi, calculator)
        
        # Employee Satisfaction Score (simulated from real data patterns)
        satisfaction_kpi = KPIMetric.objects.filter(name__icontains='satisfaction').first()
        if satisfaction_kpi:
            calculated += self.calculate_satisfaction_score(satisfaction_kpi, calculator)
        
        # Attendance Rate
        attendance_kpi = KPIMetric.objects.filter(name__icontains='attendance').first()
        if attendance_kpi:
            calculated += self.calculate_attendance_rate(attendance_kpi, calculator)
        
        # Training Hours per Employee
        training_kpi = KPIMetric.objects.filter(name__icontains='training').first()
        if training_kpi:
            calculated += self.calculate_training_hours(training_kpi, calculator)
        
        return calculated

    def calculate_financial_kpis(self, calculator):
        """Calculate financial KPIs from real transaction data"""
        calculated = 0
        
        # Monthly Revenue
        revenue_kpi = KPIMetric.objects.filter(name__icontains='revenue').first()
        if revenue_kpi:
            calculated += self.calculate_monthly_revenue(revenue_kpi, calculator)
        
        # Cash Flow
        cashflow_kpi = KPIMetric.objects.filter(name__icontains='cash flow').first()
        if cashflow_kpi:
            calculated += self.calculate_cash_flow(cashflow_kpi, calculator)
        
        # Average Invoice Processing Time
        processing_kpi = KPIMetric.objects.filter(name__icontains='invoice processing').first()
        if processing_kpi:
            calculated += self.calculate_invoice_processing_time(processing_kpi, calculator)
        
        return calculated

    def calculate_operational_kpis(self, calculator):
        """Calculate operational KPIs from real business data"""
        calculated = 0
        
        # Asset Utilization Rate (based on project activity)
        utilization_kpi = KPIMetric.objects.filter(name__icontains='utilization').first()
        if utilization_kpi:
            calculated += self.calculate_asset_utilization(utilization_kpi, calculator)
        
        # Maintenance Cost Ratio (based on expenses)
        maintenance_kpi = KPIMetric.objects.filter(name__icontains='maintenance').first()
        if maintenance_kpi:
            calculated += self.calculate_maintenance_cost_ratio(maintenance_kpi, calculator)
        
        return calculated

    def calculate_turnover_rate(self, kpi_metric, calculator):
        """Calculate employee turnover rate from real data"""
        calculated = 0
        
        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)
            
            # Count employees at start of period
            total_employees = Employee.objects.filter(
                hire_date__lte=period_start,
                is_active=True
            ).count()

            # Count employees who left during period (simplified - using inactive status)
            left_employees = Employee.objects.filter(
                hire_date__lte=period_start,
                is_active=False,
                updated_at__range=[period_start, period_end]
            ).count()
            
            # Calculate turnover rate
            if total_employees > 0:
                turnover_rate = (left_employees / total_employees) * 100
            else:
                turnover_rate = 0
            
            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(turnover_rate, 2))),
                    'calculated_by': calculator,
                    'data_source': 'employee_records',
                    'notes': f'Calculated from {total_employees} total employees, {left_employees} departures',
                    'is_manual': False
                }
            )
            
            if created:
                calculated += 1
        
        return calculated

    def calculate_attendance_rate(self, kpi_metric, calculator):
        """Calculate attendance rate from real attendance data"""
        calculated = 0
        
        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)
            
            # Count total possible working days (exclude weekends)
            total_working_days = 0
            current_date = period_start
            while current_date <= period_end:
                if current_date.weekday() not in [4, 5]:  # Exclude Friday and Saturday
                    total_working_days += 1
                current_date += timedelta(days=1)
            
            # Count actual attendance
            total_attendance = Attendance.objects.filter(
                date__range=[period_start, period_end],
                is_present=True
            ).count()
            
            # Count total employees
            total_employees = Employee.objects.filter(is_active=True).count()
            
            # Calculate attendance rate
            expected_attendance = total_employees * total_working_days
            if expected_attendance > 0:
                attendance_rate = (total_attendance / expected_attendance) * 100
            else:
                attendance_rate = 0
            
            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(attendance_rate, 2))),
                    'calculated_by': calculator,
                    'data_source': 'attendance_records',
                    'notes': f'Calculated from {total_attendance} attendance records over {total_working_days} working days',
                    'is_manual': False
                }
            )
            
            if created:
                calculated += 1
        
        return calculated

    def calculate_monthly_revenue(self, kpi_metric, calculator):
        """Calculate monthly revenue from real invoice data"""
        calculated = 0
        
        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)
            
            # Sum paid invoices for the period
            revenue = CustomerInvoice.objects.filter(
                invoice_date__range=[period_start, period_end],
                status__in=['PAID', 'PARTIAL']
            ).aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')
            
            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': revenue,
                    'calculated_by': calculator,
                    'data_source': 'customer_invoices',
                    'notes': f'Calculated from paid customer invoices',
                    'is_manual': False
                }
            )
            
            if created:
                calculated += 1
        
        return calculated

    def calculate_cash_flow(self, kpi_metric, calculator):
        """Calculate cash flow from real financial data"""
        calculated = 0
        
        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)
            
            # Calculate inflows (customer payments)
            inflows = CustomerInvoice.objects.filter(
                invoice_date__range=[period_start, period_end],
                status__in=['PAID', 'PARTIAL']
            ).aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')
            
            # Calculate outflows (vendor payments + expenses)
            vendor_outflows = VendorInvoice.objects.filter(
                invoice_date__range=[period_start, period_end],
                status='PAID'
            ).aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')
            
            expense_outflows = Expense.objects.filter(
                expense_date__range=[period_start, period_end],
                status='APPROVED'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            # Calculate net cash flow
            cash_flow = inflows - vendor_outflows - expense_outflows
            
            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': cash_flow,
                    'calculated_by': calculator,
                    'data_source': 'financial_transactions',
                    'notes': f'Inflows: {inflows}, Outflows: {vendor_outflows + expense_outflows}',
                    'is_manual': False
                }
            )
            
            if created:
                calculated += 1
        
        return calculated

    def calculate_satisfaction_score(self, kpi_metric, calculator):
        """Calculate employee satisfaction score (simulated from real data patterns)"""
        calculated = 0

        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)

            # Calculate satisfaction based on real indicators
            # - Low turnover = higher satisfaction
            # - High attendance = higher satisfaction
            # - Fewer leave requests = higher satisfaction

            total_employees = Employee.objects.filter(is_active=True).count()
            if total_employees == 0:
                satisfaction_score = 0
            else:
                # Base satisfaction score
                base_score = 3.5

                # Attendance factor
                attendance_records = Attendance.objects.filter(
                    date__range=[period_start, period_end],
                    is_present=True
                ).count()
                expected_attendance = total_employees * 22  # ~22 working days per month
                attendance_factor = min(attendance_records / expected_attendance, 1.0) if expected_attendance > 0 else 0

                # Leave request factor (fewer emergency leaves = higher satisfaction)
                emergency_leaves = LeaveRequest.objects.filter(
                    start_date__range=[period_start, period_end],
                    leave_type__name__icontains='emergency'
                ).count()
                leave_factor = max(0, 1 - (emergency_leaves / total_employees))

                # Calculate final score
                satisfaction_score = base_score + (attendance_factor * 1.0) + (leave_factor * 0.5)
                satisfaction_score = min(satisfaction_score, 5.0)  # Cap at 5.0

            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(satisfaction_score, 2))),
                    'calculated_by': calculator,
                    'data_source': 'employee_behavior_analysis',
                    'notes': f'Calculated from attendance and leave patterns',
                    'is_manual': False
                }
            )

            if created:
                calculated += 1

        return calculated

    def calculate_training_hours(self, kpi_metric, calculator):
        """Calculate training hours per employee (simulated from project complexity)"""
        calculated = 0

        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)

            # Estimate training hours based on project activity
            # More complex projects = more training needed
            active_projects = Project.objects.filter(
                start_date__lte=period_end,
                end_date__gte=period_start,
                status__in=['IN_PROGRESS', 'PLANNING']
            ).count()

            total_employees = Employee.objects.filter(is_active=True).count()

            if total_employees > 0:
                # Base training hours + project complexity factor
                base_hours = 2.0  # 2 hours per month minimum
                project_factor = active_projects * 1.5  # 1.5 hours per active project
                training_hours = base_hours + (project_factor / total_employees)
            else:
                training_hours = 0

            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(training_hours, 2))),
                    'calculated_by': calculator,
                    'data_source': 'project_complexity_analysis',
                    'notes': f'Estimated from {active_projects} active projects',
                    'is_manual': False
                }
            )

            if created:
                calculated += 1

        return calculated

    def calculate_invoice_processing_time(self, kpi_metric, calculator):
        """Calculate average invoice processing time"""
        calculated = 0

        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)

            # Calculate average time from invoice date to payment
            paid_invoices = CustomerInvoice.objects.filter(
                invoice_date__range=[period_start, period_end],
                status='PAID'
            )

            if paid_invoices.exists():
                total_processing_days = 0
                invoice_count = 0

                for invoice in paid_invoices:
                    # Estimate payment date (simplified)
                    payment_date = invoice.due_date  # Assume paid by due date
                    processing_days = (payment_date - invoice.invoice_date).days
                    total_processing_days += processing_days
                    invoice_count += 1

                avg_processing_time = total_processing_days / invoice_count if invoice_count > 0 else 0
            else:
                avg_processing_time = 0

            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(avg_processing_time, 2))),
                    'calculated_by': calculator,
                    'data_source': 'invoice_payment_analysis',
                    'notes': f'Calculated from {paid_invoices.count()} paid invoices',
                    'is_manual': False
                }
            )

            if created:
                calculated += 1

        return calculated

    def calculate_asset_utilization(self, kpi_metric, calculator):
        """Calculate asset utilization rate based on project activity"""
        calculated = 0

        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)

            # Calculate utilization based on employee project assignments
            total_employees = Employee.objects.filter(is_active=True).count()

            # Count employees assigned to active tasks
            active_tasks = Task.objects.filter(
                start_date__lte=period_end,
                due_date__gte=period_start,
                status__in=['IN_PROGRESS', 'NOT_STARTED']
            )

            assigned_employees = active_tasks.values('assigned_to').distinct().count()

            if total_employees > 0:
                utilization_rate = (assigned_employees / total_employees) * 100
            else:
                utilization_rate = 0

            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(utilization_rate, 2))),
                    'calculated_by': calculator,
                    'data_source': 'project_task_analysis',
                    'notes': f'{assigned_employees} of {total_employees} employees assigned to active tasks',
                    'is_manual': False
                }
            )

            if created:
                calculated += 1

        return calculated

    def calculate_maintenance_cost_ratio(self, kpi_metric, calculator):
        """Calculate maintenance cost ratio from expense data"""
        calculated = 0

        for month_offset in range(self.months):
            period_end = timezone.now().date() - timedelta(days=30 * month_offset)
            period_start = period_end - timedelta(days=30)

            # Calculate maintenance expenses as percentage of total expenses
            total_expenses = Expense.objects.filter(
                expense_date__range=[period_start, period_end],
                status='APPROVED'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            maintenance_expenses = Expense.objects.filter(
                expense_date__range=[period_start, period_end],
                status='APPROVED',
                category__in=['EQUIPMENT', 'OFFICE_SUPPLIES']  # Maintenance-related categories
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            if total_expenses > 0:
                maintenance_ratio = (maintenance_expenses / total_expenses) * 100
            else:
                maintenance_ratio = 0

            # Create KPI value
            kpi_value, created = KPIMetricValue.objects.update_or_create(
                kpi_metric=kpi_metric,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': Decimal(str(round(maintenance_ratio, 2))),
                    'calculated_by': calculator,
                    'data_source': 'expense_category_analysis',
                    'notes': f'Maintenance: {maintenance_expenses}, Total: {total_expenses}',
                    'is_manual': False
                }
            )

            if created:
                calculated += 1

        return calculated
